---
description: 
globs: 
alwaysApply: true
---
**Project Overview:**
We are building a Next.js application that helps UX designers iterate quickly by generating UI mockups using AI. The core interface will be a canvas powered by @xyflow/react (formerly React Flow). Users will input a text prompt, an AI (simulated for now) will generate HTML with Tailwind CSS, and this HTML will be rendered as a movable, draggable node on the React Flow canvas. Generated mockups (nodes) should be persisted in the user's LocalStorage.

**Core Technologies:**
*   Next.js (App Router)
*   React
*   TypeScript
*   @xyflow/react (for the canvas)
*   Tailwind CSS (for styling the application UI and for the generated mockups)
*   LocalStorage API
*   Sonner (for toast notifications)
