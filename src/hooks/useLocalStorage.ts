'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';

function useLocalStorage<T>(key: string, initialValue: T) {
  // State to store our value
  // Pass initial state function to useState so logic is only executed once
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }
    try {
      // Get from local storage by key
      const item = window.localStorage.getItem(key);
      // Parse stored json or if none return initialValue
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      // If error, log it and return initialValue
      console.error("Error loading from localStorage", error);
      toast.error("Failed to load data from LocalStorage.");
      return initialValue;
    }
  });

  // useEffect to update local storage when the state changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handler = setTimeout(() => {
      try {
        // Save state to local storage
        window.localStorage.setItem(key, JSON.stringify(storedValue));
      } catch (error) {
        // If error, log it and show a toast
        console.error("Error saving to localStorage", error);
        toast.error("Failed to save data to LocalStorage.");
      }
    }, 500); // 500ms debounce

    return () => {
      clearTimeout(handler);
    };
  }, [key, storedValue]);

  return [storedValue, setStoredValue] as const;
}

export default useLocalStorage;