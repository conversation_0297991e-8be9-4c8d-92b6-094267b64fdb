'use client';

import { useState, useCallback } from 'react';
import { Node } from '@xyflow/react';
import { HtmlMockupNode } from '../lib/types';

interface UseNodeSelectionReturn {
  selectedNodeId: string | null;
  selectNode: (nodeId: string) => void;
  deselectNode: () => void;
  isNodeSelected: (nodeId: string) => boolean;
}

export function useNodeSelection(): UseNodeSelectionReturn {
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);

  const selectNode = useCallback((nodeId: string) => {
    setSelectedNodeId(nodeId);
  }, []);

  const deselectNode = useCallback(() => {
    setSelectedNodeId(null);
  }, []);

  const isNodeSelected = useCallback(
    (nodeId: string) => selectedNodeId === nodeId,
    [selectedNodeId]
  );

  return {
    selectedNodeId,
    selectNode,
    deselectNode,
    isNodeSelected,
  };
} 