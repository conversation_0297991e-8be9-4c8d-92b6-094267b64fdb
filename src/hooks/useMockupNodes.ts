'use client';

import { useCallback, useEffect, useState } from 'react';
import { useNodesState } from '@xyflow/react';
import { HtmlMockupNode } from '../lib/types';
import { generateMockupHtml, generateMockupHtmlSync } from '../lib/mockupGenerator';
import { toast } from 'sonner';

const STORAGE_KEY = 'mockup-nodes';

interface HtmlCodeModalState {
  isOpen: boolean;
  nodeId: string | null;
  htmlString: string;
  nodeName: string;
}

export function useMockupNodes() {
  const [nodes, setNodes, onNodesChange] = useNodesState<HtmlMockupNode>([]);
  const [codeModal, setCodeModal] = useState<HtmlCodeModalState>({
    isOpen: false,
    nodeId: null,
    htmlString: '',
    nodeName: '',
  });

  // Load nodes from localStorage on mount
  useEffect(() => {
    const savedNodes = localStorage.getItem(STORAGE_KEY);
    if (savedNodes) {
      try {
        setNodes(JSON.parse(savedNodes));
      } catch (error) {
        console.error('Failed to load nodes from localStorage:', error);
        toast.error('Failed to load saved nodes');
      }
    }
  }, [setNodes]);

  // Save nodes to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(nodes));
  }, [nodes]);

  const addMockupNode = useCallback(
    async (prompt: string) => {
      const nodeId = `node-${Date.now()}`;

      // Create a loading node first
      const loadingNode: HtmlMockupNode = {
        id: nodeId,
        type: 'htmlMockupNode',
        position: { x: Math.random() * 500, y: Math.random() * 500 },
        data: {
          htmlString: generateMockupHtmlSync(prompt), // Loading placeholder
          onDelete: deleteMockupNode,
          onSelect: selectNode,
          onRename: renameNode,
          onViewCode: viewNodeCode,
          name: `Generating: ${prompt}`,
          isSelected: false,
          isLoading: true,
          prompt,
        },
      };

      // Add the loading node immediately
      setNodes((nds) => [...nds, loadingNode]);

      toast.info('Generating UI mockup...', {
        description: `Creating mockup for: "${prompt}"`,
      });

      try {
        // Generate HTML mockup from the prompt using AI
        const htmlString = await generateMockupHtml(prompt);

        // Update the node with the generated content
        setNodes((nds) =>
          nds.map((node) =>
            node.id === nodeId
              ? {
                  ...node,
                  data: {
                    ...node.data,
                    htmlString,
                    name: `Mockup: ${prompt}`,
                    isLoading: false,
                    error: undefined,
                  },
                }
              : node
          )
        );

        toast.success('Mockup generated successfully!', {
          description: `AI created UI for: "${prompt}"`,
        });
      } catch (error) {
        console.error('Error generating mockup:', error);

        // Update the node with error state
        setNodes((nds) =>
          nds.map((node) =>
            node.id === nodeId
              ? {
                  ...node,
                  data: {
                    ...node.data,
                    htmlString: `<div class="p-4 border border-red-300 rounded-md bg-red-50">
                      <h3 class="text-red-800 font-semibold mb-2">Generation Failed</h3>
                      <p class="text-red-600 text-sm">Failed to generate UI for: "${prompt}"</p>
                      <p class="text-red-500 text-xs mt-2">${error instanceof Error ? error.message : 'Unknown error'}</p>
                    </div>`,
                    name: `Failed: ${prompt}`,
                    isLoading: false,
                    error: error instanceof Error ? error.message : 'Unknown error',
                  },
                }
              : node
          )
        );

        toast.error('Failed to generate mockup', {
          description: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    },
    [setNodes]
  );

  const deleteMockupNode = useCallback(
    (nodeId: string) => {
      setNodes((nds) => nds.filter((node) => node.id !== nodeId));
      toast.success('Deleted mockup node');
    },
    [setNodes]
  );

  const selectNode = useCallback(
    (nodeId: string | null) => {
      setNodes((nds) =>
        nds.map((node) => ({
          ...node,
          data: {
            ...node.data,
            isSelected: nodeId !== null && node.id === nodeId,
          },
        }))
      );
    },
    [setNodes]
  );

  const renameNode = useCallback(
    (nodeId: string, name: string) => {
      setNodes((nds) =>
        nds.map((node) =>
          node.id === nodeId
            ? {
                ...node,
                data: {
                  ...node.data,
                  name,
                },
              }
            : node
        )
      );
      toast.success('Renamed mockup node');
    },
    [setNodes]
  );

  const viewNodeCode = useCallback(
    (nodeId: string) => {
      const node = nodes.find((n) => n.id === nodeId);
      if (node) {
        setCodeModal({
          isOpen: true,
          nodeId,
          htmlString: node.data.htmlString,
          nodeName: node.data.name || 'Untitled',
        });
      }
    },
    [nodes]
  );

  const closeCodeModal = useCallback(() => {
    setCodeModal((prev) => ({ ...prev, isOpen: false }));
  }, []);

  const deselectAllNodes = useCallback(() => {
    setNodes((nds) =>
      nds.map((node) => ({
        ...node,
        data: {
          ...node.data,
          isSelected: false,
        },
      }))
    );
  }, [setNodes]);

  // Update nodes with callbacks
  const nodesWithCallbacks = nodes.map(node => ({
    ...node,
    data: {
      ...node.data,
      onDelete: deleteMockupNode,
      onSelect: selectNode,
      onRename: renameNode,
      onViewCode: viewNodeCode,
    },
  }));

  return {
    nodes: nodesWithCallbacks,
    setNodes,
    onNodesChange,
    addMockupNode,
    deleteMockupNode,
    selectNode,
    renameNode,
    viewNodeCode,
    deselectAllNodes,
    codeModal,
    closeCodeModal,
  };
}