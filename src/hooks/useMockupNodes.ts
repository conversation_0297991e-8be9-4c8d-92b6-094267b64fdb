'use client';

import { useCallback, useEffect, useState } from 'react';
import { useNodesState } from '@xyflow/react';
import { HtmlMockupNode } from '../lib/types';
import { generateMockupHtml } from '../lib/mockupGenerator';
import { toast } from 'sonner';

const STORAGE_KEY = 'mockup-nodes';

interface HtmlCodeModalState {
  isOpen: boolean;
  nodeId: string | null;
  htmlString: string;
  nodeName: string;
}

export function useMockupNodes() {
  const [nodes, setNodes, onNodesChange] = useNodesState<HtmlMockupNode>([]);
  const [codeModal, setCodeModal] = useState<HtmlCodeModalState>({
    isOpen: false,
    nodeId: null,
    htmlString: '',
    nodeName: '',
  });

  // Load nodes from localStorage on mount
  useEffect(() => {
    const savedNodes = localStorage.getItem(STORAGE_KEY);
    if (savedNodes) {
      try {
        setNodes(JSON.parse(savedNodes));
      } catch (error) {
        console.error('Failed to load nodes from localStorage:', error);
        toast.error('Failed to load saved nodes');
      }
    }
  }, [setNodes]);

  // Save nodes to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(nodes));
  }, [nodes]);

  const addMockupNode = useCallback(
    (prompt: string) => {
      // Generate HTML mockup from the prompt
      const htmlString = generateMockupHtml(prompt);

      const newNode: HtmlMockupNode = {
        id: `node-${Date.now()}`,
        type: 'htmlMockupNode',
        position: { x: Math.random() * 500, y: Math.random() * 500 },
        data: {
          htmlString,
          onDelete: deleteMockupNode,
          onSelect: selectNode,
          onRename: renameNode,
          onViewCode: viewNodeCode,
          name: `Mockup: ${prompt}`, // Use the prompt as part of the default name
          isSelected: false,
        },
      };

      setNodes((nds) => [...nds, newNode]);
      toast.success('Added new mockup node', {
        description: `Generated from prompt: "${prompt}"`,
      });
    },
    [setNodes]
  );

  const deleteMockupNode = useCallback(
    (nodeId: string) => {
      setNodes((nds) => nds.filter((node) => node.id !== nodeId));
      toast.success('Deleted mockup node');
    },
    [setNodes]
  );

  const selectNode = useCallback(
    (nodeId: string | null) => {
      setNodes((nds) =>
        nds.map((node) => ({
          ...node,
          data: {
            ...node.data,
            isSelected: nodeId !== null && node.id === nodeId,
          },
        }))
      );
    },
    [setNodes]
  );

  const renameNode = useCallback(
    (nodeId: string, name: string) => {
      setNodes((nds) =>
        nds.map((node) =>
          node.id === nodeId
            ? {
                ...node,
                data: {
                  ...node.data,
                  name,
                },
              }
            : node
        )
      );
      toast.success('Renamed mockup node');
    },
    [setNodes]
  );

  const viewNodeCode = useCallback(
    (nodeId: string) => {
      const node = nodes.find((n) => n.id === nodeId);
      if (node) {
        setCodeModal({
          isOpen: true,
          nodeId,
          htmlString: node.data.htmlString,
          nodeName: node.data.name || 'Untitled',
        });
      }
    },
    [nodes]
  );

  const closeCodeModal = useCallback(() => {
    setCodeModal((prev) => ({ ...prev, isOpen: false }));
  }, []);

  const deselectAllNodes = useCallback(() => {
    setNodes((nds) =>
      nds.map((node) => ({
        ...node,
        data: {
          ...node.data,
          isSelected: false,
        },
      }))
    );
  }, [setNodes]);

  // Update nodes with callbacks
  const nodesWithCallbacks = nodes.map(node => ({
    ...node,
    data: {
      ...node.data,
      onDelete: deleteMockupNode,
      onSelect: selectNode,
      onRename: renameNode,
      onViewCode: viewNodeCode,
    },
  }));

  return {
    nodes: nodesWithCallbacks,
    setNodes,
    onNodesChange,
    addMockupNode,
    deleteMockupNode,
    selectNode,
    renameNode,
    viewNodeCode,
    deselectAllNodes,
    codeModal,
    closeCodeModal,
  };
}