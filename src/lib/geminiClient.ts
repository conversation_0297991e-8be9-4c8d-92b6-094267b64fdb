import { GoogleGenAI } from '@google/genai';

// Initialize the Gemini client
const apiKey = process.env.GEMINI_API_KEY;

if (!apiKey) {
  throw new Error('GEMINI_API_KEY is not set in environment variables');
}

export const genAI = new GoogleGenAI({ apiKey });

// Model configuration
export const MODEL_NAME = 'gemini-2.0-flash';

// Default generation config
export const DEFAULT_CONFIG = {
  temperature: 0.7,
  topP: 0.8,
  topK: 40,
  maxOutputTokens: 8192,
};

/**
 * Generate HTML mockup using Gemini AI
 */
export async function generateHtmlWithGemini(prompt: string): Promise<string> {
  try {
    const model = genAI.getGenerativeModel({ 
      model: MODEL_NAME,
      generationConfig: DEFAULT_CONFIG,
    });

    // Enhanced prompt for better UI generation
    const enhancedPrompt = `
You are a UI/UX designer and frontend developer. Generate clean, modern HTML with Tailwind CSS classes for the following request: "${prompt}"

Requirements:
- Use only Tailwind CSS classes for styling
- Create responsive, modern designs
- Use semantic HTML elements
- Include proper accessibility attributes
- Make it visually appealing with good spacing and typography
- Use appropriate colors and gradients
- Include hover effects where appropriate
- Ensure the design is mobile-friendly
- Return ONLY the HTML code without any markdown formatting or explanations

Generate HTML for: ${prompt}
`;

    const result = await model.generateContent(enhancedPrompt);
    const response = await result.response;
    const text = response.text();

    // Clean up the response to extract only HTML
    let htmlContent = text.trim();
    
    // Remove markdown code blocks if present
    htmlContent = htmlContent.replace(/```html\n?/g, '');
    htmlContent = htmlContent.replace(/```\n?/g, '');
    htmlContent = htmlContent.replace(/^```/g, '');
    htmlContent = htmlContent.replace(/```$/g, '');
    
    // If the response doesn't contain HTML tags, wrap it in a div
    if (!htmlContent.includes('<') || !htmlContent.includes('>')) {
      htmlContent = `<div class="p-4 border border-gray-300 rounded-md bg-gray-50">
        <p class="text-gray-600">Generated content: ${htmlContent}</p>
      </div>`;
    }

    return htmlContent;
  } catch (error) {
    console.error('Error generating HTML with Gemini:', error);
    
    // Return a fallback HTML with error message
    return `<div class="p-4 border border-red-300 rounded-md bg-red-50">
      <h3 class="text-red-800 font-semibold mb-2">Generation Error</h3>
      <p class="text-red-600 text-sm">Failed to generate UI for: "${prompt}"</p>
      <p class="text-red-500 text-xs mt-2">Error: ${error instanceof Error ? error.message : 'Unknown error'}</p>
    </div>`;
  }
}
