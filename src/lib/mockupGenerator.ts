import { generateHtmlWithGemini } from './geminiClient';

/**
 * Generate HTML mockup using Gemini AI
 * This function now uses the real Gemini API instead of hardcoded responses
 */
export async function generateMockupHtml(prompt: string): Promise<string> {
  try {
    return await generateHtmlWithGemini(prompt);
  } catch (error) {
    console.error('Error in generateMockupHtml:', error);

    // Fallback to a simple error display
    return `<div class="p-4 border border-red-300 rounded-md bg-red-50">
      <h3 class="text-red-800 font-semibold mb-2">Generation Failed</h3>
      <p class="text-red-600 text-sm">Unable to generate UI for: "${prompt}"</p>
      <p class="text-red-500 text-xs mt-2">Please check your API key and try again.</p>
    </div>`;
  }
}

/**
 * Legacy synchronous function for backward compatibility
 * Returns a loading placeholder that will be replaced by the async version
 */
export function generateMockupHtmlSync(prompt: string): string {
  // Return a loading placeholder
  return `<div class="p-6 border border-blue-300 rounded-md bg-blue-50 animate-pulse">
    <div class="flex items-center justify-center space-x-2">
      <div class="w-4 h-4 bg-blue-400 rounded-full animate-bounce"></div>
      <div class="w-4 h-4 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
      <div class="w-4 h-4 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
    </div>
    <p class="text-blue-700 text-center mt-4 font-medium">Generating UI for: "${prompt}"</p>
    <p class="text-blue-600 text-center text-sm mt-2">Please wait while AI creates your mockup...</p>
  </div>`;
}

/**
 * Demo function that provides some hardcoded examples for testing
 * This can be used as a fallback or for demo purposes
 */
export function generateDemoMockupHtml(prompt: string): string {
  switch (prompt.toLowerCase()) {
    case "button":
      return `<div class="flex flex-row gap-4">
      <div class="mobilescreen flex flex-col items-center justify-between p-6 bg-gradient-to-br from-blue-100 to-purple-100">
  <div class="w-full flex justify-end">
    <a href="#" class="text-gray-600 text-sm font-semibold">Skip</a>
  </div>
  <div class="flex flex-col items-center text-center flex-grow justify-center">
    <img src="https://picsum.photos/id/171/300/300" alt="Welcome illustration" class="w-64 h-64 object-cover rounded-full shadow-lg mb-8"/>
    <h1 class="text-3xl font-bold text-gray-800 mb-4">Welcome Aboard!</h1>
    <p class="text-md text-gray-600 mb-8 px-4">We're excited to have you join us. Let's get you set up in just a few steps.</p>
  </div>
  <div class="w-full">
    <div class="w-full h-2 bg-gray-300 rounded-full mb-4">
      <div class="w-1/3 h-full bg-blue-600 rounded-full"></div>
    </div>
    <p class="text-sm text-gray-600 text-center mb-4">Step 1 of 3</p>
    <button class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold text-lg shadow-md">Get Started</button>
  </div>
</div>
<div class="mobilescreen flex flex-col items-center justify-between p-6 bg-gradient-to-br from-purple-100 to-pink-100">
    <div class="w-full flex justify-end">
    <a href="#" class="text-gray-600 text-sm font-semibold">Skip</a>
  </div>
  <div class="flex flex-col items-center text-center flex-grow justify-center">
    <div class="w-24 h-24 bg-purple-600 rounded-full flex items-center justify-center mb-8">
      <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    </div>
    <h1 class="text-3xl font-bold text-gray-800 mb-4">Let's Set Up Permissions</h1>
    <p class="text-md text-gray-600 mb-8 px-4">To give you the best experience, we need a few permissions. You can manage these anytime in settings.</p>
    <div class="w-full max-w-sm text-left space-y-4">
      <div class="flex items-center p-4 bg-white rounded-lg shadow-sm">
        <div class="w-6 h-6 bg-blue-500 rounded mr-4"></div>
        <div>
          <p class="font-semibold text-gray-700">Location Access</p>
          <p class="text-sm text-gray-500">Needed for personalized features.</p>
        </div>
      </div>
      <div class="flex items-center p-4 bg-white rounded-lg shadow-sm">
        <div class="w-6 h-6 bg-green-500 rounded mr-4"></div>
        <div>
          <p class="font-semibold text-gray-700">Notifications</p>
          <p class="text-sm text-gray-500">Stay updated on important activity.</p>
        </div>
      </div>
      <div class="flex items-center p-4 bg-white rounded-lg shadow-sm">
        <div class="w-6 h-6 bg-red-500 rounded mr-4"></div>
        <div>
          <p class="font-semibold text-gray-700">Camera Access</p>
          <p class="text-sm text-gray-500">For profile pictures & sharing photos.</p>
        </div>
      </div>
    </div>
  </div>
  <div class="w-full">
     <div class="w-full h-2 bg-gray-300 rounded-full mb-4">
      <div class="w-2/3 h-full bg-purple-600 rounded-full"></div>
    </div>
     <p class="text-sm text-gray-600 text-center mb-4">Step 2 of 3</p>
    <button class="w-full bg-purple-600 text-white py-3 rounded-lg font-semibold text-lg shadow-md">Allow Permissions</button>
  </div>
</div>
<div class="mobilescreen flex flex-col items-center justify-between p-6 bg-gradient-to-br from-pink-100 to-orange-100">
   <div class="w-full flex justify-end">
    <a href="#" class="text-gray-600 text-sm font-semibold">Skip</a>
  </div>
  <div class="flex flex-col items-center text-center flex-grow justify-center w-full">
    <div class="w-24 h-24 bg-pink-600 rounded-full flex items-center justify-center mb-8">
      <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
      </svg>
    </div>
    <h1 class="text-3xl font-bold text-gray-800 mb-4">Complete Your Profile</h1>
    <p class="text-md text-gray-600 mb-8 px-4">Just a few details to finish setting up your account.</p>
    <div class="w-full max-w-sm space-y-6">
      <div>
        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
        <input type="text" id="name" placeholder="John Doe" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"/>
      </div>
      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
        <input type="email" id="email" placeholder="<EMAIL>" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"/>
      </div>
      <div>
        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
        <input type="password" id="password" placeholder="Enter secure password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"/>
      </div>
    </div>
  </div>
   <div class="w-full">
     <div class="w-full h-2 bg-gray-300 rounded-full mb-4">
      <div class="w-full h-full bg-pink-600 rounded-full"></div>
    </div>
     <p class="text-sm text-gray-600 text-center mb-4">Step 3 of 3</p>
    <button class="w-full bg-pink-600 text-white py-3 rounded-lg font-semibold text-lg shadow-md">Finish Setup</button>
  </div>
</div>
</div>
</>`;
    case "card":
      return `<div class='bg-white shadow-lg rounded-lg p-4 border border-gray-200'><h2>Generated Card</h2><p>Content for: ${prompt}</p></div>`;
    default:
      return `<div class='p-4 border border-blue-500 rounded-md bg-blue-100'><h1>Generated HTML for: ${prompt}</h1><p>This is a default mockup for an unrecognized prompt.</p></div>`;
  }
}