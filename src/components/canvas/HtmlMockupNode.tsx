'use client';

import { memo } from 'react';
import { NodeProps, NodeResizeControl, Handle, Position, NodeResizer } from '@xyflow/react';
import { HtmlMockupNodeData } from '../../lib/types';
import { NodeToolbar } from './NodeToolbar';

import '@/styles/node.css';

interface HtmlMockupNodeProps {
  id: string;
  data: HtmlMockupNodeData;
  isConnectable?: boolean;
}

const HtmlMockupNode = memo(({ id, data }: HtmlMockupNodeProps) => {
  const handleNodeClick = (event: React.MouseEvent) => {
    // Stop propagation to prevent canvas from handling the click
    event.stopPropagation();
    
    // If we have a selection handler in data, call it
    if (data.onSelect) {
      data.onSelect(id);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    // Handle Enter or Space to select/deselect
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault(); // Prevent scroll on space
      if (data.onSelect) {
        data.onSelect(id);
      }
    }

    // Handle Delete or Backspace to delete node
    if ((event.key === 'Delete' || event.key === 'Backspace') && data.isSelected) {
      event.preventDefault();
      data.onDelete(id);
    }

    // Handle Escape to deselect
    if (event.key === 'Escape' && data.isSelected && data.onSelect) {
      data.onSelect(null); // Passing null to deselect
    }
  };

  return (
    <>
      <NodeToolbar
        nodeId={id}
        isVisible={Boolean(data.isSelected)}
        name={data.name}
        onRename={(name) => data.onRename?.(id, name)}
        onViewCode={() => data.onViewCode?.(id)}
        onDelete={() => data.onDelete(id)}
      />
      <NodeResizer
        //color="#ff0071"
        isVisible={data.isSelected}
        minWidth={100}
        minHeight={30}
        handleStyle={{ backgroundColor: 'black', width: '10px', height: '10px' }}
        lineStyle={{ backgroundColor: 'blue' }}
        
      />
      <div className='overflow-auto h-[100%]'>
      <div
        className={`p-4 border rounded-md shadow-sm bg-transparent relative transition-all duration-200 h-[100%] ${
          data.isSelected ? 'node-selected' : 'border-gray-300'
        } ${data.isLoading ? 'border-blue-300 bg-blue-50' : ''} ${data.error ? 'border-red-300 bg-red-50' : ''}`}
        onClick={handleNodeClick}
        onKeyDown={handleKeyDown}
        role="button"
        tabIndex={0}
        aria-selected={data.isSelected}
      >
        
        {/* Only show delete button on mobile or if specifically enabled */}
        {(typeof window !== 'undefined' && window.innerWidth <= 768) && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              data.onDelete(id);
            }}
            className="nodrag absolute top-1 right-1 bg-red-500 text-white rounded-full h-5 w-5 flex items-center justify-center text-xs cursor-pointer"
            aria-label="Delete node"
          >
            &#x2715; {/* X icon */}
          </button>
        )}
        <div dangerouslySetInnerHTML={{ __html: data.htmlString }} />
      </div>
      </div>
    </>
  );
});

HtmlMockupNode.displayName = 'HtmlMockupNode';

export default HtmlMockupNode;