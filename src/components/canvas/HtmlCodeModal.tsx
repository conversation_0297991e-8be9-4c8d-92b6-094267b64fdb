'use client';

import { useCallback } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { toast } from 'sonner';
import { Copy } from 'lucide-react';

interface HtmlCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
  htmlString: string;
  nodeName: string;
}

export function HtmlCodeModal({
  isOpen,
  onClose,
  htmlString,
  nodeName,
}: HtmlCodeModalProps) {
  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(htmlString);
      toast.success('Code copied to clipboard');
    } catch (error) {
      console.error('Failed to copy code:', error);
      toast.error('Failed to copy code');
    }
  }, [htmlString]);

  // Format the HTML string with proper indentation
  const formattedHtml = htmlString
    .replace(/></g, '>\n<') // Add newlines between tags
    .replace(/\n\s*\n/g, '\n') // Remove empty lines
    .split('\n')
    .map(line => line.trim()) // Remove leading/trailing whitespace
    .join('\n');

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>HTML Code - {nodeName}</DialogTitle>
          <DialogDescription>
            View and copy the HTML code for this mockup.
          </DialogDescription>
        </DialogHeader>
        <div className="flex-grow overflow-auto relative">
          <Button
            onClick={handleCopy}
            className="absolute top-2 right-2 z-10 bg-white text-slate-950 hover:bg-slate-100 shadow-md flex gap-2 items-center"
            size="sm"
          >
            <Copy size={14} />
            Copy Code
          </Button>
          <pre className="bg-slate-950 text-slate-50 p-4 rounded-md overflow-x-auto">
            <code>{formattedHtml}</code>
          </pre>
        </div>
      </DialogContent>
    </Dialog>
  );
} 