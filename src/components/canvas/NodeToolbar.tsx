'use client';

import { NodeToolbar as ReactFlowNodeToolbar, Position } from '@xyflow/react';
import { useCallback } from 'react';

interface NodeToolbarProps {
  nodeId: string;
  isVisible: boolean;
  onRename?: (name: string) => void;
  onViewCode?: () => void;
  onDelete?: () => void;
  name?: string;
}

export function NodeToolbar({
  nodeId,
  isVisible,
  onRename,
  onViewCode,
  onDelete,
  name = '',
}: NodeToolbarProps) {
  const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;

  const handleNameChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      onRename?.(event.target.value);
    },
    [onRename]
  );

  const handleInputInteraction = useCallback((event: React.MouseEvent | React.KeyboardEvent) => {
    event.stopPropagation();
  }, []);

  return (
    <ReactFlowNodeToolbar
      isVisible={isVisible}
      position={Position.Top}
      align="center"
      className={`bg-white p-2 rounded-lg shadow-lg border border-gray-200 flex gap-2 items-center toolbar-enter ${
        isMobile ? 'toolbar-mobile' : ''
      }`}
    >
      <input
        type="text"
        value={name}
        onChange={handleNameChange}
        placeholder="Node name"
        className="nodrag nopan px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
        aria-label="Node name"
        onClick={handleInputInteraction}
        onMouseDown={handleInputInteraction}
        onKeyDown={handleInputInteraction}
        onDoubleClick={handleInputInteraction}
      />
      
      <button
        onClick={onViewCode}
        className="px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-sm transition-colors"
        aria-label="View HTML code"
      >
        View Code
      </button>

      {/* Only show delete button on mobile */}
      {isMobile && (
        <button
          onClick={onDelete}
          className="px-2 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-sm transition-colors"
          aria-label="Delete node"
        >
          Delete
        </button>
      )}
    </ReactFlowNodeToolbar>
  );
} 