'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface PromptControlAreaProps {
  addMockupNode: (prompt: string) => Promise<void>;
}

export default function PromptControlArea({ addMockupNode }: PromptControlAreaProps) {
  const [promptText, setPromptText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerateClick = async () => {
    if (!promptText.trim() || isGenerating) {
      return;
    }

    setIsGenerating(true);
    try {
      await addMockupNode(promptText.trim());
      setPromptText(''); // Clear the input after generating
    } catch (error) {
      console.error('Error generating mockup:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey && !isGenerating) {
      e.preventDefault();
      handleGenerateClick();
    }
  };

  return (
    <div className="flex w-full justify-center items-center gap-4 p-2 bg-white/50 backdrop-blur-sm rounded-lg">
      <Input
        type="text"
        placeholder="Describe the UI you want to generate (e.g., 'login form', 'pricing card', 'dashboard header')..."
        value={promptText}
        onChange={(e) => setPromptText(e.target.value)}
        onKeyDown={handleKeyDown}
        className="bg-white"
        disabled={isGenerating}
      />
      <Button
        onClick={handleGenerateClick}
        disabled={!promptText.trim() || isGenerating}
      >
        {isGenerating ? (
          <>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            Generating...
          </>
        ) : (
          'Generate UI'
        )}
      </Button>
    </div>
  );
}