'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface PromptControlAreaProps {
  addMockupNode: (prompt: string) => void;
}

export default function PromptControlArea({ addMockupNode }: PromptControlAreaProps) {
  const [promptText, setPromptText] = useState('');

  const handleGenerateClick = () => {
    if (!promptText.trim()) {
      return;
    }
    addMockupNode(promptText.trim());
    setPromptText(''); // Clear the input after generating
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleGenerateClick();
    }
  };

  return (
    <div className="flex w-full justify-center items-center gap-4 p-2 bg-white/50 backdrop-blur-sm rounded-lg">
      <Input
        type="text"
        placeholder="Try 'button' or 'card' for demo mockups..."
        value={promptText}
        onChange={(e) => setPromptText(e.target.value)}
        onKeyDown={handleKeyDown}
        className="bg-white"
      />
      <Button 
        onClick={handleGenerateClick}
        disabled={!promptText.trim()}
      >
        Generate UI
      </Button>
    </div>
  );
}