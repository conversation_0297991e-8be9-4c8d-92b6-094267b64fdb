TITLE: Using useNodesState and useEdgesState Hooks (JavaScript)
DESCRIPTION: This snippet illustrates a more concise way to manage nodes and edges in a controlled React Flow setup using the new `useNodesState` and `useEdgesState` hooks. These hooks simplify state management by providing the state, setter, and change handler in a single return value.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_2

LANGUAGE: js
CODE:
```
const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
```

----------------------------------------

TITLE: Typing ReactFlow Hooks with Custom Node and Edge Types (TSX)
DESCRIPTION: This snippet illustrates how to apply CustomNodeType and CustomEdgeType to various xyflow/react hooks, such as useReactFlow, useStore, useNodeConnections, and useNodesData. By typing these hooks, the returned nodes, edges, and their data are correctly inferred, allowing for type-safe operations and property access within the component.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/typescript.mdx#_snippet_7

LANGUAGE: TSX
CODE:
```
import { useReactFlow, useNodeConnections, useNodesData, useStore } from '@xyflow/react';

export default function FlowComponent() {
  // returned nodes and edges are correctly typed now
  const { getNodes, getEdges } = useReactFlow<CustomNodeType, CustomEdgeType>();

  // You can type useStore by typing the selector function
  const nodes = useStore((s: ReactFlowState<CustomNodeType>) => ({
    nodes: s.nodes,
  }));

  const connections = useNodeConnections({
    handleType: 'target',
  });

  const nodesData = useNodesData<CustomNodeType>(connections?.[0].source);

  nodeData.forEach(({ type, data }) => {
    if (type === 'number') {
      // This is type safe because we have narrowed down the type
      console.log(data.number);
    }
  });
  // ...
}
```

----------------------------------------

TITLE: Basic ReactFlow Component Usage in TypeScript
DESCRIPTION: This snippet illustrates the fundamental setup of the <ReactFlow /> component within a React application. It imports `ReactFlow` from `@xyflow/react` and demonstrates its rendering with placeholder props for `nodes`, `edges`, and `onNodesChange`, which are crucial for defining the flow's structure and handling state changes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/react-flow.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import { ReactFlow } from '@xyflow/react'

export default function Flow() {
  return <ReactFlow
    nodes={...}
    edges={...}
    onNodesChange={...}
    ...
  />
}
```

----------------------------------------

TITLE: Installing React Flow with npm
DESCRIPTION: This snippet demonstrates how to install the @xyflow/react package using npm. It's a fundamental step for integrating React Flow into any Node.js or React project, ensuring all necessary dependencies are available.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/getting-started/installation-and-requirements.mdx#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @xyflow/react
```

----------------------------------------

TITLE: Basic SvelteFlow Component Usage - Svelte
DESCRIPTION: This snippet demonstrates the basic setup of the <SvelteFlow /> component. It imports the component and its styles, defines initial nodes and edges using Svelte's reactive state, and renders the component binding the state variables and enabling the fitView option.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/svelte-flow.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script>
  import { SvelteFlow } from '@xyflow/svelte';
  import '@xyflow/svelte/dist/style.css';

  let nodes = $state.raw([
    { id: 'a', data: { label: 'node a' }, position: { x: 0, y: 0 } },
    { id: 'b', data: { label: 'node b' }, position: { x: 0, y: 100 } }
  ]);

  let edges = $state.raw([
    { id: 'e1-2', source: 'a', target: 'b' }
  ]);
</script>

<SvelteFlow
  bind:nodes
  bind:edges
  fitView
/>
```

----------------------------------------

TITLE: Creating Basic React Flow Component (JSX)
DESCRIPTION: This JSX code demonstrates a minimal React Flow setup. It imports the ReactFlow component and its stylesheet, defines initial nodes and edges, and renders the component within a container with defined dimensions.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/index.mdx#_snippet_3

LANGUAGE: JSX
CODE:
```
import React from 'react';
import { ReactFlow } from '@xyflow/react';

import '@xyflow/react/dist/style.css';

const initialNodes = [
  { id: '1', position: { x: 0, y: 0 }, data: { label: '1' } },
  { id: '2', position: { x: 0, y: 100 }, data: { label: '2' } },
];
const initialEdges = [{ id: 'e1-2', source: '1', target: '2' }];

export default function App() {
  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <ReactFlow nodes={initialNodes} edges={initialEdges} />
    </div>
  );
}

```

----------------------------------------

TITLE: Adding Basic Interactivity with React Flow Hooks (JSX)
DESCRIPTION: This snippet shows how to make a React Flow graph interactive. It uses the `useNodesState` and `useEdgesState` hooks to manage node and edge states and provides the necessary `onNodesChange`, `onEdgesChange`, and `onConnect` callbacks to the `ReactFlow` component. The `onConnect` callback utilizes the `addEdge` helper function.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/index.mdx#_snippet_4

LANGUAGE: jsx
CODE:
```
import React, { useCallback } from 'react';
import { ReactFlow, useNodesState, useEdgesState, addEdge } from '@xyflow/react';

import '@xyflow/react/dist/style.css';

const initialNodes = [
  { id: '1', position: { x: 0, y: 0 }, data: { label: '1' } },
  { id: '2', position: { x: 0, y: 100 }, data: { label: '2' } },
];
const initialEdges = [{ id: 'e1-2', source: '1', target: '2' }];

export default function App() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges],
  );

  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
      />
    </div>
  );
}
```

----------------------------------------

TITLE: Applying Node Changes in ReactFlow with applyNodeChanges
DESCRIPTION: This snippet demonstrates how to use `applyNodeChanges` within a React component to manage node state. It integrates with the `onNodesChange` prop of the `<ReactFlow />` component, taking an array of `NodeChange` objects and updating the `nodes` state using `setNodes`. This provides a straightforward way to handle node updates without custom logic.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/apply-node-changes.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { useState, useCallback } from 'react';
import { ReactFlow, applyNodeChanges } from '@xyflow/react';

export default function Flow() {
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  const onNodesChange = useCallback(
    (changes) => {
      setNodes((oldNodes) => applyNodeChanges(changes, oldNodes));
    },
    [setNodes],
  );

  return (
    <ReactFlow nodes={nodes} edges={edges} onNodesChange={onNodesChange} />
  );
}
```

----------------------------------------

TITLE: Defining a Basic Node Array (JavaScript)
DESCRIPTION: Creates an array named `nodes` containing a single node object. Each node requires a unique `id`, a `position` object with `x` and `y` coordinates, and a `data` object containing information like a `label` to be displayed.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/getting-started/building-a-flow.mdx#_snippet_1

LANGUAGE: js
CODE:
```
const nodes = [
  {
    id: '1', // required
    position: { x: 0, y: 0 }, // required
    data: { label: 'Hello' }, // required
  },
];
```

----------------------------------------

TITLE: Specifying Custom Edge Types with useEdges in TypeScript
DESCRIPTION: This example illustrates how to use the `useEdges` hook with a generic type argument in TypeScript to specify a custom edge type (`CustomEdgeType`). This allows for type-safe access to edges that conform to a specific interface or class, enhancing type checking and developer experience.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-edges.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
const nodes = useEdges<CustomEdgeType>();
```

----------------------------------------

TITLE: Installing React Flow with yarn
DESCRIPTION: This snippet illustrates how to install the @xyflow/react package using yarn. Yarn is another popular package manager that offers fast, reliable, and secure dependency management for JavaScript projects.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/getting-started/installation-and-requirements.mdx#_snippet_2

LANGUAGE: bash
CODE:
```
yarn add @xyflow/react
```

----------------------------------------

TITLE: Importing ReactFlow and Styles (New API)
DESCRIPTION: This snippet shows the new way to import React Flow components and styles in version 12. The `reactflow` package is now `@xyflow/react` and requires named imports. It also includes the new style import paths for either full or basic styles.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_2

LANGUAGE: javascript
CODE:
```
// npm install @xyflow/react
import { ReactFlow } from '@xyflow/react';

// you also need to adjust the style import
import '@xyflow/react/dist/style.css';

// or if you just want basic styles
import '@xyflow/react/dist/base.css';
```

----------------------------------------

TITLE: Updating Nodes Immutably (New API)
DESCRIPTION: This snippet shows the recommended way to update nodes in React Flow 12. Updates must be immutable, meaning a new node object should be created (e.g., using spread syntax) instead of directly mutating existing ones, ensuring proper state management.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_8

LANGUAGE: javascript
CODE:
```
setNodes((currentNodes) =>
  currentNodes.map((node) => ({
    ...node,
    hidden: true
  }))
);
```

----------------------------------------

TITLE: Adding Minimap, Controls, and Background to React Flow (JSX)
DESCRIPTION: This example extends the basic interactive graph by adding the built-in `<Controls />`, `<MiniMap />`, and `<Background />` components as children to the `<ReactFlow />` component. It uses the same state management and callback logic for interactivity as the previous example.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/index.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
import React, { useCallback } from 'react';
import {
  ReactFlow,
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
} from '@xyflow/react';

import '@xyflow/react/dist/style.css';

const initialNodes = [
  { id: '1', position: { x: 0, y: 0 }, data: { label: '1' } },
  { id: '2', position: { x: 0, y: 100 }, data: { label: '2' } },
];
const initialEdges = [{ id: 'e1-2', source: '1', target: '2' }];

export default function App() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges],
  );

  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
      >
        <Controls />
        <MiniMap />
        <Background variant="dots" gap={12} size={1} />
      </ReactFlow>
    </div>
  );
}
```

----------------------------------------

TITLE: Creating a Custom Node Component (Svelte)
DESCRIPTION: This Svelte component defines a custom node named `TextUpdaterNode`. It receives node properties like `id` and `data`, uses the `useSvelteFlow` hook to access `updateNodeData`, and includes an input field that updates the node's data when its value changes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/customization/custom-nodes.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { Position, useSvelteFlow, type NodeProps } from '@xyflow/svelte';

  let { id, data }: NodeProps = $props();

  let { updateNodeData } = useSvelteFlow();
</script>

<div class="text-updater-node">
  <div>
    <label for="text">Text:</label>
    <input
      id="text"
      name="text"
      value={data.text}
      oninput={(evt) => {
        updateNodeData(id, { text: evt.target.value });
      }}
      class="nodrag"
    />
  </div>
</div>
```

----------------------------------------

TITLE: Defining OnEdgesChange Type in TypeScript
DESCRIPTION: This snippet defines the `OnEdgesChange` type, a generic TypeScript type used to specify the signature of the `onEdgesChange` function in React Flow. It expects an array of `EdgeChange` objects, which describe modifications to edges, and returns void. This type ensures proper type checking for edge manipulation callbacks.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/on-edges-change.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
export type OnEdgesChange<EdgeType extends Edge = Edge> = (
  changes: EdgeChange<EdgeType>[],
) => void;
```

----------------------------------------

TITLE: Implementing Basic React Flow with TypeScript
DESCRIPTION: This snippet demonstrates the fundamental setup of a React Flow component using TypeScript. It defines initial nodes and edges, sets up state management with `useState` and `useCallback` for node/edge changes and connections, and configures `ReactFlow` with various options like `fitViewOptions` and `defaultEdgeOptions`. It showcases explicit type definitions for nodes, edges, and event handlers.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/typescript.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import { useState, useCallback } from 'react';
import {
  ReactFlow,
  addEdge,
  applyNodeChanges,
  applyEdgeChanges,
  type Node,
  type Edge,
  type FitViewOptions,
  type OnConnect,
  type OnNodesChange,
  type OnEdgesChange,
  type OnNodeDrag,
  type DefaultEdgeOptions,
} from '@xyflow/react';

const initialNodes: Node[] = [
  { id: '1', data: { label: 'Node 1' }, position: { x: 5, y: 5 } },
  { id: '2', data: { label: 'Node 2' }, position: { x: 5, y: 100 } },
];

const initialEdges: Edge[] = [{ id: 'e1-2', source: '1', target: '2' }];

const fitViewOptions: FitViewOptions = {
  padding: 0.2,
};

const defaultEdgeOptions: DefaultEdgeOptions = {
  animated: true,
};

const onNodeDrag: OnNodeDrag = (_, node) => {
  console.log('drag event', node.data);
};

function Flow() {
  const [nodes, setNodes] = useState<Node[]>(initialNodes);
  const [edges, setEdges] = useState<Edge[]>(initialEdges);

  const onNodesChange: OnNodesChange = useCallback(
    (changes) => setNodes((nds) => applyNodeChanges(changes, nds)),
    [setNodes],
  );
  const onEdgesChange: OnEdgesChange = useCallback(
    (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [setEdges],
  );
  const onConnect: OnConnect = useCallback(
    (connection) => setEdges((eds) => addEdge(connection, eds)),
    [setEdges],
  );

  return (
    <ReactFlow
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onConnect={onConnect}
      onNodeDrag={onNodeDrag}
      fitView
      fitViewOptions={fitViewOptions}
      defaultEdgeOptions={defaultEdgeOptions}
    />
  );
}
```

----------------------------------------

TITLE: Importing React Flow Components and Stylesheet (JavaScript)
DESCRIPTION: Imports necessary components (`ReactFlow`, `Background`, `Controls`) from `@xyflow/react` and the required stylesheet to render a basic, empty flow with controls and background. The stylesheet import is crucial for React Flow to function correctly.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/getting-started/building-a-flow.mdx#_snippet_0

LANGUAGE: js
CODE:
```
import { ReactFlow, Background, Controls } from '@xyflow/react';
import '@xyflow/react/dist/style.css';
```

----------------------------------------

TITLE: Creating a Custom Edge with EdgeLabelRenderer in React Flow
DESCRIPTION: This React component, `CustomEdge`, demonstrates how to render a custom edge with a div-based label using `@xyflow/react`. It utilizes `getBezierPath` to calculate the edge path and label position, `BaseEdge` for the SVG path, and `EdgeLabelRenderer` as a portal to render a styled HTML `div` element as the edge label. The label is positioned absolutely and styled with a yellow background, padding, and rounded corners, and includes `nodrag nopan` classes to prevent unwanted interactions.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/edge-label-renderer.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import React from 'react';
import { getBezierPath, EdgeLabelRenderer, BaseEdge } from '@xyflow/react';

const CustomEdge = ({ id, data, ...props }) => {
  const [edgePath, labelX, labelY] = getBezierPath(props);

  return (
    <>
      <BaseEdge id={id} path={edgePath} />
      <EdgeLabelRenderer>
        <div
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            background: '#ffcc00',
            padding: 10,
            borderRadius: 5,
            fontSize: 12,
            fontWeight: 700,
          }}
          className="nodrag nopan"
        >
          {data.label}
        </div>
      </EdgeLabelRenderer>
    </>
  );
};

export default CustomEdge;
```

----------------------------------------

TITLE: Importing React Flow Component and Styles in JavaScript
DESCRIPTION: After installing the React Flow package, this snippet demonstrates how to import the ReactFlow component and its essential CSS styles into your application. Both imports are crucial for rendering and styling React Flow diagrams correctly within a React component.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/getting-started/installation-and-requirements.mdx#_snippet_4

LANGUAGE: js
CODE:
```
import { ReactFlow } from '@xyflow/react';
import '@xyflow/react/dist/style.css';
```

----------------------------------------

TITLE: Installing Svelte Flow Package (npm)
DESCRIPTION: Installs the `@xyflow/svelte` package using npm. This is the primary way to add Svelte Flow to your project. Requires Node.js and npm/yarn/pnpm/bun.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/getting-started/installation.mdx#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @xyflow/svelte
```

----------------------------------------

TITLE: Initializing Nodes Array with $state.raw (JavaScript)
DESCRIPTION: Initializes a reactive array of node objects using Svelte's `$state.raw` rune. Each node requires an `id`, `position` (x, y coordinates), and `data` object for custom properties like a label. Using `$state.raw` prevents deep reactivity for performance.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/getting-started/building-a-flow.mdx#_snippet_2

LANGUAGE: javascript
CODE:
```
let nodes = $state.raw([
  {
    id: '1',
    position: { x: 0, y: 0 },
    data: { label: 'Hello' },
  },
  {
    id: '2',
    position: { x: 100, y: 100 },
    data: { label: 'World' },
  },
]);
```

----------------------------------------

TITLE: Setting Initial Viewport with defaultViewport (v11)
DESCRIPTION: This JSX snippet illustrates the new approach for setting the initial viewport in React Flow v11 using the `defaultViewport` prop. It combines position (`x`, `y`) and zoom into a single `Viewport` object, simplifying viewport configuration. This is the recommended method for v11.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v11.mdx#_snippet_4

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';

const defaultViewport: Viewport = { x: 10, y: 15, zoom: 5 };

const Flow = () => {
  return <ReactFlow defaultViewport={defaultViewport} />;
};

export default Flow;
```

----------------------------------------

TITLE: Scaffolding React Flow Project with degit
DESCRIPTION: This snippet shows how to quickly initialize a new React Flow project using npx degit. It fetches the official Vite and TypeScript template for React Flow, providing a ready-to-use project structure without cloning the entire Git history.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/getting-started/installation-and-requirements.mdx#_snippet_5

LANGUAGE: bash
CODE:
```
npx degit xyflow/vite-react-flow-template your-app-name
```

----------------------------------------

TITLE: Using Svelte Flow Vite Template (degit)
DESCRIPTION: Clones the `vite-svelte-flow-template` repository using `npx degit`. This provides a quick starting point for a Svelte Flow project with Vite. Requires Node.js and npx.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/getting-started/installation.mdx#_snippet_2

LANGUAGE: bash
CODE:
```
npx degit xyflow/vite-svelte-flow-template your-app-name
```

----------------------------------------

TITLE: Defining Node Types Inside Component (Warning) - JSX
DESCRIPTION: This example demonstrates an anti-pattern where `nodeTypes` are defined directly inside a React component's render function. This creates a new object on every re-render, causing React Flow to unnecessarily re-render and trigger a warning. It leads to performance issues and should be avoided.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_3

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import MyCustomNode from './MyCustomNode';

function Flow(props) {
  // new object being created on every render
  // causing unnecessary re-renders
  const nodeTypes = {
    myCustomNode: MyCustomNode,
  };

  return <ReactFlow nodeTypes={nodeTypes} />;
}

export default Flow;
```

----------------------------------------

TITLE: Defining the Viewport Type in React Flow (TypeScript)
DESCRIPTION: This snippet defines the `Viewport` type in TypeScript, which represents the current state of the React Flow canvas. It includes `x` and `y` for the top-left corner's coordinates in the internal system, and `zoom` for the current zoom level. This type is crucial for understanding the visible area and scale of the flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/viewport.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type Viewport = {
  x: number;
  y: number;
  zoom: number;
};
```

----------------------------------------

TITLE: Using useNodesState and useEdgesState for Controlled ReactFlow
DESCRIPTION: This example demonstrates how to use the `useNodesState` and `useEdgesState` hooks to manage the state of nodes and edges in a controlled React Flow instance. It initializes empty arrays for nodes and edges and passes the state and change handlers to the `ReactFlow` component, enabling external state management.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-edges-state.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { ReactFlow, useNodesState, useEdgesState } from '@xyflow/react';

const initialNodes = [];
const initialEdges = [];

export default function () {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  return (
    <ReactFlow
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
    />
  );
}
```

----------------------------------------

TITLE: Applying Edge Changes in ReactFlow with applyEdgeChanges
DESCRIPTION: This example demonstrates how to use the `applyEdgeChanges` utility with React's `useState` and `useCallback` hooks to manage edge updates in a ReactFlow component. The `onEdgesChange` handler receives an array of `EdgeChange` objects and applies them to the current edges state, ensuring the flow's edges are updated correctly based on user interactions or other events.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/apply-edge-changes.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { useState, useCallback } from 'react';
import { ReactFlow, applyEdgeChanges } from '@xyflow/react';

export default function Flow() {
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  const onEdgesChange = useCallback(
    (changes) => {
      setEdges((oldEdges) => applyEdgeChanges(changes, oldEdges));
    },
    [setEdges],
  );

  return (
    <ReactFlow nodes={nodes} edges={edges} onEdgesChange={onEdgesChange} />
  );
}
```

----------------------------------------

TITLE: Importing Svelte Flow Component and Styles (JS)
DESCRIPTION: Imports the main `SvelteFlow` component and the necessary CSS styles from the installed package. This is required to use Svelte Flow in your Svelte application.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/getting-started/installation.mdx#_snippet_1

LANGUAGE: js
CODE:
```
import { SvelteFlow } from '@xyflow/svelte';
import '@xyflow/svelte/dist/style.css';
```

----------------------------------------

TITLE: Defining Custom Node and Edge Type Unions in ReactFlow (TSX)
DESCRIPTION: This snippet defines CustomNodeType and CustomEdgeType unions by combining BuiltInNode and BuiltInEdge from @xyflow/react with custom node and edge types (NumberNode, TextNode, EditableEdge). This allows for comprehensive type checking across all node and edge variations used in the application.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/typescript.mdx#_snippet_5

LANGUAGE: TSX
CODE:
```
import type { BuiltInNode, BuiltInEdge } from '@xyflow/react';

// Custom nodes
import NumberNode from './NumberNode';
import TextNode from './TextNode';

// Custom edge
import EditableEdge from './EditableEdge';

export type CustomNodeType = BuiltInNode | NumberNode | TextNode;
export type CustomEdgeType = BuiltInEdge | EditableEdge;
```

----------------------------------------

TITLE: Initializing a Basic Svelte Flow
DESCRIPTION: This snippet demonstrates how to set up a basic Svelte Flow component in a Svelte file, importing necessary modules, defining initial nodes and edges using Svelte 5's $state.raw, and rendering the flow with background, controls, and minimap.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/src/content/svelte-flow-release.mdx#_snippet_0

LANGUAGE: Svelte
CODE:
```
<script>
  import { SvelteFlow, Background, Controls, Minimap } from '@xyflow/svelte';
  import '@xyflow/svelte/dist/style.css';

  let nodes = $state.raw([
    {
      id: '1',
      type: 'input',
      position: { x: 0, y: 0 },
      data: { label: 'Hello' },
    },
    {
      id: '2',
      type: 'output',
      position: { x: 100, y: 100 },
      data: { label: 'World' },
    },
  ]);

  let edges = $state.raw([
    { id: '1-2', source: '1', target: '2', type: 'smoothstep', label: 'to the' },
  ]);
</script>

<SvelteFlow bind:nodes bind:edges fitView>
  <Background />
  <Controls />
  <Minimap />
</SvelteFlow>
```

----------------------------------------

TITLE: Binding Edges to Svelte Flow (Svelte)
DESCRIPTION: Binds the `edges` array initialized in the script block to the `SvelteFlow` component using Svelte's `bind:` directive, in addition to the existing node binding. This enables two-way data binding for the edges array.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/getting-started/building-a-flow.mdx#_snippet_5

LANGUAGE: svelte
CODE:
```
<SvelteFlow bind:nodes bind:edges>
```

----------------------------------------

TITLE: Memoizing Node Types with useMemo in React Flow
DESCRIPTION: This snippet demonstrates how to correctly define `nodeTypes` inside a React component using the `useMemo` hook. Memoizing the `nodeTypes` object is crucial to prevent React from creating a new object on every render, which would lead to performance issues and bugs in your React Flow application.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/custom-nodes.mdx#_snippet_2

LANGUAGE: jsx
CODE:
```
const nodeTypes = useMemo(() => ({ textUpdater: TextUpdaterNode }), []);

return <ReactFlow nodeTypes={nodeTypes} />;
```

----------------------------------------

TITLE: Handling Missing `nodeTypes` Prop in React Flow (Incorrect)
DESCRIPTION: This snippet demonstrates an incorrect way to use custom nodes in React Flow. It defines a node with `type: 'custom'` but fails to pass the `nodeTypes` prop to the `ReactFlow` component, preventing the custom node component from being rendered.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_6

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import MyCustomNode from './MyCustomNode';

const nodes = [
  {
    id: 'mycustomnode',
    type: 'custom',
    // ...
  },
];

function Flow(props) {
  // nodeTypes property is missing, so React Flow cannot find the custom node component to render
  return <ReactFlow nodes={nodes} />;
}
```

----------------------------------------

TITLE: Integrating Zustand Store with React Flow Component in JSX
DESCRIPTION: This snippet demonstrates how to integrate the Zustand store with a React Flow component. It uses a `selector` function with `shallow` equality to efficiently extract `nodes`, `edges`, and their corresponding change handlers (`onNodesChange`, `onEdgesChange`, `addEdge`) from the store, preventing unnecessary re-renders. The `ReactFlow` component is then configured with these state and action props.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_8

LANGUAGE: jsx
CODE:
```
import React from 'react';
import { ReactFlow, Background } from '@xyflow/react';
import { shallow } from 'zustand/shallow';

import { useStore } from './store';

const selector = (store) => ({
  nodes: store.nodes,
  edges: store.edges,
  onNodesChange: store.onNodesChange,
  onEdgesChange: store.onEdgesChange,
  addEdge: store.addEdge,
});

export default function App() {
  const store = useStore(selector, shallow);

  return (
    <ReactFlow
      nodes={store.nodes}
      edges={store.edges}
      onNodesChange={store.onNodesChange}
      onEdgesChange={store.onEdgesChange}
      onConnect={store.addEdge}
    >
      <Background />
    </ReactFlow>
  );
}
```

----------------------------------------

TITLE: Counting Nodes with useReactFlow in React
DESCRIPTION: This example demonstrates how to use the `useReactFlow` hook to get the current number of nodes in a React Flow instance. It utilizes `useState` and `useCallback` to manage and update the node count when a button is clicked. The `reactFlow.getNodes().length` method is used to query the current state of the flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-react-flow.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useCallback, useState } from 'react';
import { useReactFlow } from '@xyflow/react';

export function NodeCounter() {
  const reactFlow = useReactFlow();
  const [count, setCount] = useState(0);
  const countNodes = useCallback(() => {
    setCount(reactFlow.getNodes().length);
    // you need to pass it as a dependency if you are using it with useEffect or useCallback
    // because at the first render, it's not initialized yet and some functions might not work.
  }, [reactFlow]);

  return (
    <div>
      <button onClick={countNodes}>Update count</button>
      <p>There are {count} nodes in the flow.</p>
    </div>
  );
}
```

----------------------------------------

TITLE: Using useNodesState and useEdgesState Hooks in React Flow
DESCRIPTION: This snippet demonstrates how to use the `useNodesState` and `useEdgesState` helper hooks provided by React Flow to manage the state of nodes and edges. These hooks simplify the process of setting up and updating the `nodes` and `edges` arrays, along with their respective change handlers.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/concepts/core-concepts.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
```

----------------------------------------

TITLE: Importing Base Svelte Flow Styles (JavaScript)
DESCRIPTION: This snippet imports the minimal set of base CSS styles essential for Svelte Flow's core functionality. These styles are required for the library to work correctly, and overriding them without providing alternatives may lead to unexpected behavior.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/customization/theming.mdx#_snippet_1

LANGUAGE: javascript
CODE:
```
import '@xyflow/svelte/dist/base.css';
```

----------------------------------------

TITLE: Defining Zustand Action to Add Child Node (TypeScript)
DESCRIPTION: Defines a Zustand store action named `addChildNode` responsible for adding a new node and an edge connecting it to a specified parent node. The action generates unique IDs for the new node and edge using `nanoid`, sets the new node's type to 'mindmap' and its parent ID, and updates the store's state by spreading the existing nodes and edges arrays and adding the new elements.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_13

LANGUAGE: ts
CODE:
```
addChildNode: (parentNode: Node, position: XYPosition) => {
  const newNode = {
    id: nanoid(),
    type: 'mindmap',
    data: { label: 'New Node' },
    position,
    parentNode: parentNode.id,
  };

  const newEdge = {
    id: nanoid(),
    source: parentNode.id,
    target: newNode.id,
  };

  set({
    nodes: [...get().nodes, newNode],
    edges: [...get().edges, newEdge],
  });
};
```

----------------------------------------

TITLE: Fitting View After Node Addition (New Method) - React Flow - TypeScript
DESCRIPTION: This snippet showcases the improved `fitView` behavior in React Flow 12.5.0, where it now works immediately and synchronously after updating nodes. This eliminates the need for `requestAnimationFrame` or `setTimeout`, providing a seamless user experience without intermediate unfitted views.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/whats-new/2025-03-27.mdx#_snippet_1

LANGUAGE: TypeScript
CODE:
```
setNodes((nodes) => [nodes, ...newNode]);
fitView();
```

----------------------------------------

TITLE: Defining a Basic Edge Array (JavaScript)
DESCRIPTION: Creates an array named `edges` containing a single edge object. Each edge requires a unique `id` and specifies the `source` and `target` node IDs that it connects, establishing a relationship between two nodes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/getting-started/building-a-flow.mdx#_snippet_3

LANGUAGE: js
CODE:
```
const edges = [{ id: '1-2', source: '1', target: '2' }];
```

----------------------------------------

TITLE: Initializing Node and Edge State (React/JavaScript)
DESCRIPTION: Initializes the state for nodes and edges using the `useState` hook. It sets up state variables `nodes` and `edges` with initial values (`initialNodes`, `initialEdges`) and their corresponding setter functions `setNodes` and `setEdges`. This is the first step in managing the flow's elements as controlled components.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/getting-started/adding-interactivity.mdx#_snippet_1

LANGUAGE: javascript
CODE:
```
const [nodes, setNodes] = useState(initialNodes);
const [edges, setEdges] = useState(initialEdges);
```

----------------------------------------

TITLE: Installing React Flow with pnpm
DESCRIPTION: This snippet shows how to install the @xyflow/react package using pnpm. pnpm is an efficient package manager that can save disk space by using a content-addressable store, making it a good alternative for managing project dependencies.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/getting-started/installation-and-requirements.mdx#_snippet_1

LANGUAGE: bash
CODE:
```
pnpm add @xyflow/react
```

----------------------------------------

TITLE: Using useOnSelectionChange to Display Selected Elements (JSX)
DESCRIPTION: This example demonstrates how to use the `useOnSelectionChange` hook to capture and display the IDs of currently selected nodes and edges in a React Flow application. It highlights the critical requirement to memoize the `onChange` callback using `useCallback` to ensure the hook functions correctly and prevents unnecessary re-renders.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-on-selection-change.mdx#_snippet_0

LANGUAGE: JSX
CODE:
```
import { useState } from 'react';
import { ReactFlow, useOnSelectionChange } from '@xyflow/react';

function SelectionDisplay() {
  const [selectedNodes, setSelectedNodes] = useState([]);
  const [selectedEdges, setSelectedEdges] = useState([]);

  // the passed handler has to be memoized, otherwise the hook will not work correctly
  const onChange = useCallback(({ nodes, edges }) => {
    setSelectedNodes(nodes.map((node) => node.id));
    setSelectedEdges(edges.map((edge) => edge.id));
  }, []);

  useOnSelectionChange({
    onChange,
  });

  return (
    <div>
      <p>Selected nodes: {selectedNodes.join(', ')}</p>
      <p>Selected edges: {selectedEdges.join(', ')}</p>
    </div>
  );
}
```

----------------------------------------

TITLE: Typing ReactFlow Callbacks with Custom Node Types (TSX)
DESCRIPTION: This snippet demonstrates how to explicitly type callback functions like onNodeDrag and onNodesChange by passing the CustomNodeType union to the OnNodeDrag and OnNodesChange generics. This ensures that TypeScript correctly infers the node.data type within the callback, enabling type-safe access to custom node properties.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/typescript.mdx#_snippet_6

LANGUAGE: TSX
CODE:
```
import { type OnNodeDrag } from '@xyflow/react';

// ...

// Pass your union type here ...
const onNodeDrag: OnNodeDrag<CustomNodeType> = useCallback((_, node) => {
  if (node.type === 'number') {
    // From here on, Typescript knows that node.data
    // is of type { num: number }
    console.log('drag event', node.data.number);
  }
}, []);

const onNodesChange: OnNodesChange<CustomNodeType> = useCallback(
  (changes) => setNodes((nds) => applyNodeChanges(changes, nds)),
  [setNodes],
);
```

----------------------------------------

TITLE: Installing React Flow with New Package Name - npm
DESCRIPTION: This snippet demonstrates how to install the new `reactflow` package using npm. This is a crucial step for users migrating to v11, as the package name has changed from previous versions.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/src/content/react-flow-v11.mdx#_snippet_0

LANGUAGE: bash
CODE:
```
npm install reactflow
```

----------------------------------------

TITLE: Correct React Flow State Access with External Provider - JSX
DESCRIPTION: This snippet illustrates the correct pattern for accessing React Flow's internal state using `useReactFlow`. The `ReactFlowProvider` wraps the component that uses the hook, ensuring that the hook is called within the provider's context. This allows successful access to the React Flow instance.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_2

LANGUAGE: jsx
CODE:
```
import { ReactFlow, ReactFlowProvider } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

function Flow(props) {
  // you can access the internal state here
  const reactFlowInstance = useReactFlow();

  return <ReactFlow {...props} />;
}

// wrapping with ReactFlowProvider is done outside of the component
function FlowWithProvider(props) {
  return (
    <ReactFlowProvider>
      <Flow {...props} />
    </ReactFlowProvider>
  );
}

export default FlowWithProvider;
```

----------------------------------------

TITLE: Implementing Edge Reconnection in Svelte
DESCRIPTION: This Svelte code snippet demonstrates how to use the <EdgeReconnectAnchor /> component within a custom edge component to enable reconnection. It imports necessary components and functions from '@xyflow/svelte', defines derived edge path, manages reconnection state, and conditionally renders the base edge and reconnection anchors based on the edge's selected state.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/edge-reconnect-anchor.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import {
    BaseEdge,
    EdgeReconnectAnchor,
    getBezierPath,
    type EdgeProps,
  } from '@xyflow/svelte';

  let { sourceX, sourceY, targetX, targetY, selected, data, ...props }: EdgeProps = $props();

  const [edgePath] = $derived(getBezierPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
  }));

  let reconnecting = $state(false);
</script>

<!-- We want to hide the initial edge while reconnecting -->
{#if !reconnecting}
  <BaseEdge path={edgePath} {...props} />
{/if}

<!-- We only want to be able to reconnect when an edge is selected  -->
{#if selected}
  <EdgeReconnectAnchor
    bind:reconnecting
    type="source"
    position={{ x: sourceX, y: sourceY }}
  />
  <EdgeReconnectAnchor
    bind:reconnecting
    type="target"
    position={{ x: targetX, y: targetY }}
  />
{/if}
```

----------------------------------------

TITLE: Connecting React Flow App to Zustand Store - TSX
DESCRIPTION: Demonstrates how to use the `useStore` hook with a selector and `shallow` comparison to retrieve nodes, edges, and change handlers from the Zustand store. These values are then passed as props to the `ReactFlow` component, along with configuration for `nodeOrigin` and `fitView`. Requires `@xyflow/react` styles.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_7

LANGUAGE: tsx
CODE:
```
import { ReactFlow, Controls, Panel, NodeOrigin } from '@xyflow/react';
import { shallow } from 'zustand/shallow';

import useStore, { RFState } from './store';

// we have to import the React Flow styles for it to work
import '@xyflow/react/dist/style.css';

const selector = (state: RFState) => ({
  nodes: state.nodes,
  edges: state.edges,
  onNodesChange: state.onNodesChange,
  onEdgesChange: state.onEdgesChange,
});

// this places the node origin in the center of a node
const nodeOrigin: NodeOrigin = [0.5, 0.5];

function Flow() {
  // whenever you use multiple values, you should use shallow to make sure the component only re-renders when one of the values changes
  const { nodes, edges, onNodesChange, onEdgesChange } = useStore(selector, shallow);

  return (
    <ReactFlow
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      nodeOrigin={nodeOrigin}
      fitView
    >
      <Controls showInteractive={false} />
      <Panel position="top-left">React Flow Mind Map</Panel>
    </ReactFlow>
  );
}

export default Flow;
```

----------------------------------------

TITLE: Creating a Basic Straight Custom Edge in Svelte Flow
DESCRIPTION: This snippet shows the basic structure of a custom edge component in Svelte Flow. It uses the BaseEdge component to render the SVG path and the getStraightPath utility function to calculate the path coordinates between the source and target nodes. It receives edge properties like id, source/target coordinates via EdgeProps.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/customization/custom-edges.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { BaseEdge, getStraightPath, type EdgeProps } from '@xyflow/svelte';

  let { id, sourceX, sourceY, targetX, targetY }: EdgeProps = $props();

  let [edgePath] = $derived(
    getStraightPath({
      sourceX,
      sourceY,
      targetX,
      targetY,
    })
  );
</script>

<BaseEdge {id} path={edgePath} />
```

----------------------------------------

TITLE: Typing useReactFlow Hook with Custom Node and Edge Types in TypeScript
DESCRIPTION: This snippet illustrates how to apply generic type arguments to the `useReactFlow` hook in TypeScript. By specifying `CustomNodeType` and `CustomEdgeType`, developers can ensure type safety when interacting with custom node and edge structures within their React Flow application.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-react-flow.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
const reactFlow = useReactFlow<CustomNodeType, CustomEdgeType>();
```

----------------------------------------

TITLE: Installing React Flow with bun
DESCRIPTION: This snippet provides the command to install the @xyflow/react package using bun. Bun is a new JavaScript runtime, bundler, transpiler, and package manager designed for speed, offering a modern alternative for project setup.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/getting-started/installation-and-requirements.mdx#_snippet_3

LANGUAGE: bash
CODE:
```
bun add @xyflow/react
```

----------------------------------------

TITLE: Adding New Audio Nodes to Store in JavaScript
DESCRIPTION: This store action generates a unique ID for a new node and creates it based on the `type` ('osc' or 'amp') with hardcoded initial data and position. It calls `createAudioNode` to instantiate the Web Audio node and then updates the store's `nodes` array.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_30

LANGUAGE: javascript
CODE:
```
import { ..., createAudioNode } from './audio';

export const useStore = createWithEqualityFn((set, get) => ({
  ...

  createNode(type) {
    const id = nanoid();

    switch(type) {
      case 'osc': {
        const data = { frequency: 440, type: 'sine' };
        const position = { x: 0, y: 0 };

        createAudioNode(id, type, data);
        set({ nodes: [...get().nodes, { id, type, data, position }] });

        break;
      }

      case 'amp': {
        const data = { gain: 0.5 };
        const position = { x: 0, y: 0 };

        createAudioNode(id, type, data);
        set({ nodes: [...get().nodes, { id, type, data, position }] });

        break;
      }
    }
  }
}));
```

----------------------------------------

TITLE: Setting Up React Flow with Custom Nodes and Data Edges
DESCRIPTION: This comprehensive snippet sets up a React Flow application. It defines custom `NumNode` and `SumNode` types, initializes a set of nodes and data-driven edges, and configures the `onConnect` handler to automatically create new edges of type 'data' with a 'value' key, enabling data visualization on connections.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_21

LANGUAGE: TypeScript
CODE:
```
import '@xyflow/react/dist/style.css';

import {
  ReactFlow,
  OnConnect,
  Position,
  useNodesState,
  useEdgesState,
  addEdge,
  Edge,
  Node,
} from '@xyflow/react';

import { NumNode } from '@/components/nodes/num-node';
import { SumNode } from '@/components/nodes/sum-node';

import { DataEdge } from '@/components/data-edge';

const nodeTypes = {
  num: NumNode,
  sum: SumNode,
};

const initialNodes: Node[] = [
  { id: 'a', type: 'num', data: { value: 0 }, position: { x: 0, y: 0 } },
  { id: 'b', type: 'num', data: { value: 0 }, position: { x: 0, y: 200 } },
  { id: 'c', type: 'sum', data: { value: 0 }, position: { x: 300, y: 100 } },
  { id: 'd', type: 'num', data: { value: 0 }, position: { x: 0, y: 400 } },
  { id: 'e', type: 'sum', data: { value: 0 }, position: { x: 600, y: 400 } },
];

const edgeTypes = {
  data: DataEdge,
};

const initialEdges: Edge[] = [
  {
    id: 'a->c',
    type: 'data',
    data: { key: 'value' },
    source: 'a',
    target: 'c',
    targetHandle: 'x',
  },
  {
    id: 'b->c',
    type: 'data',
    data: { key: 'value' },
    source: 'b',
    target: 'c',
    targetHandle: 'y',
  },
  {
    id: 'c->e',
    type: 'data',
    data: { key: 'value' },
    source: 'c',
    target: 'e',
    targetHandle: 'x',
  },
  {
    id: 'd->e',
    type: 'data',
    data: { key: 'value' },
    source: 'd',
    target: 'e',
    targetHandle: 'y',
  },
];

function App() {
  const [nodes, , onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const onConnect: OnConnect = useCallback(
    (params) => {
      setEdges((edges) =>
        addEdge({ type: 'data', data: { key: 'value' }, ...params }, edges),
      );
    },
    [setEdges],
  );

  return (
    <div className="h-screen w-screen p-8">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
      />
    </div>
  );
}

export default App;
```

----------------------------------------

TITLE: Defining Node and Edge Change Handlers (React/JavaScript)
DESCRIPTION: Defines memoized callback functions `onNodesChange` and `onEdgesChange` using `useCallback`. These functions receive change events and use `applyNodeChanges` and `applyEdgeChanges` respectively to update the `nodes` and `edges` state. These handlers are crucial for responding to user interactions like dragging or selecting elements.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/getting-started/adding-interactivity.mdx#_snippet_2

LANGUAGE: javascript
CODE:
```
const onNodesChange = useCallback(
  (changes) => setNodes((nds) => applyNodeChanges(changes, nds)),
  [],
);
const onEdgesChange = useCallback(
  (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
  [],
);
```

----------------------------------------

TITLE: Using ReactFlowProvider with ReactFlow and useNodes Hook (TypeScript)
DESCRIPTION: This example demonstrates how to use the <ReactFlowProvider /> component to wrap <ReactFlow /> and enable the use of React Flow hooks like useNodes in child components (e.g., a Sidebar). It shows how to access and display node positions from the flow's internal state. The useNodes hook requires the component to be a child of <ReactFlowProvider /> to function correctly.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/react-flow-provider.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { ReactFlow, ReactFlowProvider, useNodes } from '@xyflow/react'

export default function Flow() {
  return (
    <ReactFlowProvider>
      <ReactFlow nodes={...} edges={...} />
      <Sidebar />
    </ReactFlowProvider>
  )
}

function Sidebar() {
  // This hook will only work if the component it's used in is a child of a
  // <ReactFlowProvider />.
  const nodes = useNodes()

  return (
    <aside>
      {nodes.map((node) => (
        <div key={node.id}>
          Node {node.id} -
            x: {node.position.x.toFixed(2)},
            y: {node.position.y.toFixed(2)}
        </div>
      ))}
    </aside>
  )
}
```

----------------------------------------

TITLE: Defining Custom Node Types in React Flow (TypeScript)
DESCRIPTION: This TypeScript snippet illustrates how to extend the generic `Node` type in React Flow. By providing a second type parameter, developers can specify a custom node type, enabling more precise type definitions for node `data` structures and their associated `type` property. This improves type safety and organization for custom node implementations.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/src/content/react-flow-v-11-5.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
type MyCustomNode = Node<MyCustomNodeData, 'custom-node-type'>;
```

----------------------------------------

TITLE: Custom Mind Map Node Component (TSX)
DESCRIPTION: React component `MindMapNode` for a custom node type in React Flow. It uses the `NodeProps` type and includes an input field bound to the node's `data.label`. Changes to the input trigger the `updateNodeLabel` action from the Zustand store. It also includes `Handle` components for connections.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_17

LANGUAGE: tsx
CODE:
```
import { Handle, NodeProps, Position } from '@xyflow/react';

import useStore from '../store';

export type NodeData = {
  label: string;
};

function MindMapNode({ id, data }: NodeProps<NodeData>) {
  const updateNodeLabel = useStore((state) => state.updateNodeLabel);

  return (
    <>
      <input
        // from now on we can use value instead of defaultValue
        // this makes sure that the input always shows the current label of the node
        value={data.label}
        onChange={(evt) => updateNodeLabel(id, evt.target.value)}
        className="input"
      />

      <Handle type="target" position={Position.Top} />
      <Handle type="source" position={Position.Top} />
    </>
  );
}

export default MindMapNode;
```

----------------------------------------

TITLE: Implementing Type Guards for Custom ReactFlow Nodes (TSX)
DESCRIPTION: This snippet demonstrates how to create a TypeScript type guard function, isNumberNode, to narrow down the type of a CustomNodeType to NumberNode. This function returns true if the node's type property is 'number', allowing filter operations on node arrays to produce a type-safe array of NumberNode instances.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/typescript.mdx#_snippet_8

LANGUAGE: TSX
CODE:
```
function isNumberNode(node: CustomNodeType): node is NumberNode {
  return node.type === 'number';
}

// numberNodes is of type NumberNode[]
const numberNodes = nodes.filter(isNumberNode);
```

----------------------------------------

TITLE: Defining the Edge Type in React Flow (TypeScript)
DESCRIPTION: This snippet defines the `Edge` type in React Flow as a union of `DefaultEdge`, `SmoothStepEdge`, and `BezierEdge` types. It represents the complete description of an edge, allowing for different rendering behaviors based on the specific edge variant.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/edge.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type Edge<T> = DefaultEdge<T> | SmoothStepEdge<T> | BezierEdge<T>;
```

----------------------------------------

TITLE: Defining the EdgeChange Type in TypeScript
DESCRIPTION: This type definition represents the `EdgeChange` union type used by the `onEdgesChange` callback in xyflow. It combines four distinct change types: `EdgeAddChange` for adding edges, `EdgeRemoveChange` for removing edges, `EdgeReplaceChange` for replacing edges, and `EdgeSelectionChange` for changes in edge selection state. This union allows the `onEdgesChange` handler to process various edge modifications uniformly.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/edge-change.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type EdgeChange =
  | EdgeAddChange
  | EdgeRemoveChange
  | EdgeReplaceChange
  | EdgeSelectionChange;
```

----------------------------------------

TITLE: Importing Hooks and React Flow Helpers (JavaScript)
DESCRIPTION: Imports the `useState` and `useCallback` hooks from React, and the `ReactFlow` component along with `applyEdgeChanges` and `applyNodeChanges` helper functions from the `@xyflow/react` library. These are necessary for managing state and handling changes in the flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/getting-started/adding-interactivity.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { useState, useCallback } from 'react';
import { ReactFlow, applyEdgeChanges, applyNodeChanges } from '@xyflow/react';
```

----------------------------------------

TITLE: Implementing a Custom Node Component in React Flow
DESCRIPTION: This snippet demonstrates how to create a custom node component in React Flow. It uses `Handle` components for source and target connections and includes an input field with an `onChange` handler. React Flow automatically injects essential props like `id`, `position`, and `data` into the component.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/custom-nodes.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useCallback } from 'react';
import { Handle, Position } from '@xyflow/react';

const handleStyle = { left: 10 };

function TextUpdaterNode({ data }) {
  const onChange = useCallback((evt) => {
    console.log(evt.target.value);
  }, []);

  return (
    <>
      <Handle type="target" position={Position.Top} />
      <div>
        <label htmlFor="text">Text:</label>
        <input id="text" name="text" onChange={onChange} className="nodrag" />
      </div>
      <Handle type="source" position={Position.Bottom} id="a" />
      <Handle type="source" position={Position.Bottom} id="b" style={handleStyle} />
    </>
  );
}
```

----------------------------------------

TITLE: Implementing a Basic Straight Custom Edge in React Flow
DESCRIPTION: This snippet defines a `CustomEdge` React component that renders a straight SVG path between two connected nodes. It utilizes React Flow's `getStraightPath` utility to calculate the path coordinates and `BaseEdge` to render the SVG element, accepting `id`, `sourceX`, `sourceY`, `targetX`, and `targetY` as props.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/custom-edges.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { BaseEdge, getStraightPath } from '@xyflow/react';

export default function CustomEdge({ id, sourceX, sourceY, targetX, targetY }) {
  const [edgePath] = getStraightPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
  });

  return (
    <>
      <BaseEdge id={id} path={edgePath} />
    </>
  );
}
```

----------------------------------------

TITLE: ReactFlowInstance Type Definition - TypeScript
DESCRIPTION: This TypeScript type defines the `ReactFlowInstance` object, which provides a comprehensive API for interacting with a React Flow diagram. It includes methods for managing nodes (e.g., `getNode`, `addNodes`, `setNodes`), edges (e.g., `getEdge`, `addEdges`, `setEdges`), handling intersections (`getIntersectingNodes`, `isNodeIntersecting`), and controlling the viewport (`zoomIn`, `fitView`, `setCenter`, `screenToFlowPosition`). This instance is typically obtained via the `useReactFlow` hook or the `onInit` event.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/react-flow-instance.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type ReactFlowInstance<T, U> = {
  // Nodes and Edges
  getNode: (id: string) => Node<T> | undefined;
  getNodes: () => Node<T>[];
  addNodes: (payload: Node<T>[] | Node<T>) => void;
  setNodes: (payload: Node<T>[] | ((nodes: Node<T>[]) => Node<T>[])) => void;

  getEdge: (id: string) => Edge<U> | undefined;
  getEdges: () => Edge<U>[];
  addEdges: (payload: Edge<U>[] | Edge<U>) => void;
  setEdges: (payload: Edge<U>[] | ((edges: Edge<U>[]) => Edge<U>[])) => void;

  toObject: () => ReactFlowJsonObject<T, U>;
  deleteElements: (payload: {
    nodes?: (Partial<Node> & { id: Node['id'] })[];
    edges?: (Partial<Edge> & { id: Edge['id'] })[];
  }) => void;
  getNodesBounds: (nodes: (NodeType | InternalNode | string)[]) => Rect;

  // Intersections
  getIntersectingNodes: (
    node: (Partial<Node<T>> & { id: Node['id'] }) | Rect,
    partially?: boolean,
    nodes?: Node<T>[],
  ) => Node<T>[];

  isNodeIntersecting: (
    node: (Partial<Node<T>> & { id: Node['id'] }) | Rect,
    area: Rect,
    partially?: boolean,
  ) => boolean;

  // Viewport
  viewportInitialized: boolean;
  zoomIn: (options?: { duration: number }) => void;
  zoomOut: (options?: { duration: number }) => void;
  zoomTo: (zoomLevel: number, options?: { duration: number }) => void;
  getZoom: () => number;
  setViewport: (viewport: Viewport, options?: { duration: number }) => void;
  getViewport: () => Viewport;
  fitView: (fitViewOptions?: FitViewOptions) => boolean;
  setCenter: (
    x: number,
    y: number,
    options?: { duration: number; zoom: number },
  ) => void;
  fitBounds: (
    bounds: Rect,
    options?: { duration: number; padding: number },
  ) => void;
  screenToFlowPosition: (position: { x: number; y: number }) => {
    x: number;
    y: number;
  };
  flowToScreenPosition: (position: { x: number; y: number }) => {
    x: number;
    y: number;
  };
  updateNode: (
    id: string,
    nodeUpdate: Partial<NodeType> | ((node: NodeType) => Partial<NodeType>),
    options?: { replace: boolean },
  ) => void;
  updateNodeData: (
    id: string,
    dataUpdate:
      | Partial<NodeType>['data']
      | ((node: Node) => Partial<NodeType>['data']),
    options?: { replace: boolean },
  ) => void;
  updateEdge: (
    id: string,
    edgeUpdate: Partial<EdgeType> | ((node: EdgeType) => Partial<EdgeType>),
    options?: { replace: boolean },
  ) => void;
  updateEdgeData: (
    id: string,
    dataUpdate:
      | Partial<EdgeType>['data']
      | ((node: Edge) => Partial<EdgeType>['data']),
    options?: { replace: boolean },
  ) => void;
};
```

----------------------------------------

TITLE: Handling Connection End Event (React Flow)
DESCRIPTION: Implements the `onConnectEnd` callback function for the React Flow instance. It checks if the connection ends on a node and, if so, focuses the input field within that node. If the connection ends on the pane, it adds a new child node.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_22

LANGUAGE: tsx
CODE:
```
const onConnectEnd: OnConnectEnd = useCallback(
  (event) => {
    const { nodeLookup } = store.getState();
    const targetIsPane = (event.target as Element).classList.contains('react-flow__pane');
    const node = (event.target as Element).closest('.react-flow__node');

    if (node) {
      node.querySelector('input')?.focus({ preventScroll: true });
    } else if (targetIsPane && connectingNodeId.current) {
      const parentNode = nodeLookup.get(connectingNodeId.current);
      const childNodePosition = getChildNodePosition(event, parentNode);

      if (parentNode && childNodePosition) {
        addChildNode(parentNode, childNodePosition);
      }
    }
  },
  [getChildNodePosition],
);
```

----------------------------------------

TITLE: Adding an Edge using addEdge in Svelte
DESCRIPTION: This snippet demonstrates how to use the `addEdge` utility function from `@xyflow/svelte` to add a new edge object to an array of edges. It shows importing the function, initializing a reactive edges array using `$state.raw`, creating a new edge object, and calling `addEdge` with the new edge and the current edges array to get the updated array.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/utils/add-edge.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { addEdge } from '@xyflow/svelte';

let edges = $state.raw([]);

const onAddEdge = () => {
  const newEdge = {
    id: '1-2',
    source: '1',
    target: '2',
  };
  edges = addEdge(newEdge, edges.current);
};
```

----------------------------------------

TITLE: Providing Parent Container Dimensions for React Flow (Correct)
DESCRIPTION: This snippet shows the correct way to render React Flow by ensuring its parent container has a defined height. Providing explicit dimensions (e.g., `height: 800`) allows React Flow to accurately measure the DOM element and render the graph without warnings.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_10

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';

function Flow(props) {
  return (
    <div style={{ height: 800 }}>
      <ReactFlow {...props} />
    </div>
  );
}
```

----------------------------------------

TITLE: Subscribing to Node Data Changes in React
DESCRIPTION: This snippet demonstrates how to use the `useNodesData` hook from `@xyflow/react` to subscribe to changes in a node's data. It shows examples for subscribing to a single node's data by ID and multiple nodes' data by providing an array of IDs. This hook is essential for components that need to react to specific node data updates.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-nodes-data.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useNodesData } from '@xyflow/react';

export default function () {
  const nodeData = useNodesData('nodeId-1');

  const nodesData = useNodesData(['nodeId-1', 'nodeId-2']);
}
```

----------------------------------------

TITLE: Defining Custom Node Data Type Separately
DESCRIPTION: This snippet illustrates how to define the data structure for a custom node separately using a `type` alias. This approach enhances readability and reusability, ensuring that the `Node` type correctly incorporates the specific data structure for a custom node.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/typescript.mdx#_snippet_2

LANGUAGE: ts
CODE:
```
type NumberNodeData = { number: number };
type NumberNode = Node<NumberNodeData, 'number'>;
```

----------------------------------------

TITLE: Handling Multiple Custom Node Types with a Union in React Flow
DESCRIPTION: This example demonstrates how to create a single custom node component (`CustomNode`) that renders different content based on a union of node types (`AppNode`). It uses type narrowing (`data.type === 'number'`) to differentiate between `NumberNode` and `TextNode` data structures, providing a flexible way to manage various custom node types within one component.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/typescript.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
import type { Node, NodeProps } from '@xyflow/react';

type NumberNode = Node<{ number: number }, 'number'>;
type TextNode = Node<{ text: string }, 'text'>;

type AppNode = NumberNode | TextNode;

export default function CustomNode({ data }: NodeProps<AppNode>) {
  if (data.type === 'number') {
    return <div>A special number: {data.number}</div>;
  }

  return <div>A special text: {data.text}</div>;
}
```

----------------------------------------

TITLE: Defining Custom Node Data and Type with TypeScript
DESCRIPTION: Provides a TypeScript-specific example of defining a separate type for custom node data (`NumberNodeData`) and then using it to define the full custom node type (`NumberNodeType`) by extending the base `Node` type. Highlights the requirement to use `type` instead of `interface` in this context.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/advanced/typescript.mdx#_snippet_2

LANGUAGE: ts
CODE:
```
type NumberNodeData = { number: number };
type NumberNodeType = Node<NumberNodeData, 'number'>;
```

----------------------------------------

TITLE: Filtering Connected Edges with getConnectedEdges in React Flow
DESCRIPTION: This example demonstrates how to use the `getConnectedEdges` utility from `@xyflow/react` to filter an array of edges, returning only those that connect to the specified `nodes` array. It takes an array of nodes and an array of all edges as input, and outputs an array containing only the edges connected to the input nodes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/get-connected-edges.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { getConnectedEdges } from '@xyflow/react';

const nodes = [
  { id: 'a', position: { x: 0, y: 0 } },
  { id: 'b', position: { x: 100, y: 0 } }
];
const edges = [
  { id: 'a->c', source: 'a', target: 'c' },
  { id: 'c->d', source: 'c', target: 'd' }
];

const connectedEdges = getConnectedEdges(nodes, edges);
// => [{ id: 'a->c', source: 'a', target: 'c' }]
```

----------------------------------------

TITLE: Implementing Resizable Nodes with NodeResizer in React
DESCRIPTION: This React component, ResizableNode, demonstrates how to integrate the <NodeResizer /> component from @xyflow/react to enable resizing functionality for a custom node. It sets minimum width and height constraints for resizing and includes standard Handle components for connecting to other nodes. The component is memoized for performance optimization.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/node-resizer.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { memo } from 'react';
import { Handle, Position, NodeResizer } from '@xyflow/react';

const ResizableNode = ({ data }) => {
  return (
    <>
      <NodeResizer minWidth={100} minHeight={30} />
      <Handle type="target" position={Position.Left} />
      <div style={{ padding: 10 }}>{data.label}</div>
      <Handle type="source" position={Position.Right} />
    </>
  );
};

export default memo(ResizableNode);
```

----------------------------------------

TITLE: Creating Custom Mind Map Node Component - TSX
DESCRIPTION: Defines a custom React component (`MindMapNode`) for nodes of type 'mindmap'. It uses `NodeProps` to access node data, includes an input element for the node label, and adds `Handle` components for target (top) and source (bottom) connections, which are essential for drawing edges in React Flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_8

LANGUAGE: tsx
CODE:
```
import { Handle, NodeProps, Position } from '@xyflow/react';

export type NodeData = {
  label: string;
};

function MindMapNode({ id, data }: NodeProps<NodeData>) {
  return (
    <>
      <input defaultValue={data.label} />

      <Handle type="target" position={Position.Top} />
      <Handle type="source" position={Position.Bottom} />
    </>
  );
}

export default MindMapNode;
```

----------------------------------------

TITLE: Adding Edges to React Flow Graph with addEdge() in JavaScript
DESCRIPTION: This React component demonstrates how to use the `addEdge` utility function from `@xyflow/react` to manage connections in a React Flow graph. It utilizes `useNodesState` and `useEdgesState` to manage the graph's state and `useCallback` to define an `onConnect` handler. The `addEdge` function is called within `onConnect` to safely add new connections, performing validation to prevent duplicates or invalid edges.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/add-edge.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { useCallback } from 'react';
import {
  ReactFlow,
  addEdge,
  useNodesState,
  useEdgesState,
} from '@xyflow/react';

export default function Flow() {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const onConnect = useCallback(
    (connection) => {
      setEdges((oldEdges) => addEdge(connection, oldEdges));
    },
    [setEdges],
  );

  return <ReactFlow nodes={nodes} edges={edges} onConnect={onConnect} />;
}
```

----------------------------------------

TITLE: Using NodeResizer in a Svelte Custom Node
DESCRIPTION: This snippet demonstrates how to integrate the NodeResizer component into a custom Svelte node. It imports necessary modules like Handle, Position, and NodeResizer from '@xyflow/svelte' and sets minimum width and height for resizing.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/node-resizer.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { Handle, Position, NodeResizer, type NodeProps } from '@xyflow/svelte';

  let { data } : NodeProps = $props();
</script>

<NodeResizer minWidth={100} minHeight={30} />

<Handle type="target" position={Position.Left} />
<div style={{ padding: 10 }}>{data.label}</div>
<Handle type="source" position={Position.Right} />
```

----------------------------------------

TITLE: Implementing a Custom Number Node in React Flow
DESCRIPTION: This `NumNode` component defines a custom React Flow node that displays and manipulates a numeric value. It includes buttons for incrementing and decrementing the value, a dropdown menu for resetting or deleting the node, and a labeled source handle for connections. It depends on `BaseNode`, `LabeledHandle`, `NodeHeader` components, and shadcn/ui `Button` and `DropdownMenuItem`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_17

LANGUAGE: tsx
CODE:
```
import React, { useCallback } from 'react';
import { type Node, type NodeProps, Position, useReactFlow } from '@xyflow/react';

import { BaseNode } from '@/components/base-node';
import { LabeledHandle } from '@/components/labeled-handle';
import {
  NodeHeader,
  NodeHeaderTitle,
  NodeHeaderActions,
  NodeHeaderMenuAction,
} from '@/components/node-header';
import { Button } from '@/components/ui/button';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';

export type NumNode = Node<{
  value: number;
}>;

export function NumNode({ id, data }: NodeProps<NumNode>) {
  const { updateNodeData, setNodes } = useReactFlow();

  const handleReset = useCallback(() => {
    updateNodeData(id, { value: 0 });
  }, [id, updateNodeData]);

  const handleDelete = useCallback(() => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
  }, [id, setNodes]);

  const handleIncr = useCallback(() => {
    updateNodeData(id, { value: data.value + 1 });
  }, [id, data.value, updateNodeData]);

  const handleDecr = useCallback(() => {
    updateNodeData(id, { value: data.value - 1 });
  }, [id, data.value, updateNodeData]);

  return (
    <BaseNode>
      <NodeHeader>
        <NodeHeaderTitle>Num</NodeHeaderTitle>
        <NodeHeaderActions>
          <NodeHeaderMenuAction label="Open node menu">
            <DropdownMenuItem onSelect={handleReset}>Reset</DropdownMenuItem>
            <DropdownMenuItem onSelect={handleDelete}>Delete</DropdownMenuItem>
          </NodeHeaderMenuAction>
        </NodeHeaderActions>
      </NodeHeader>

      <div className="flex gap-2 items-center mb-10">
        <Button onClick={handleDecr}>-</Button>
        <pre>{String(data.value).padStart(3, ' ')}</pre>
        <Button onClick={handleIncr}>+</Button>
      </div>

      <footer className="bg-gray-100 -m-5">
        <LabeledHandle title="out" type="source" position={Position.Right} />
      </footer>
    </BaseNode>
  );
}
```

----------------------------------------

TITLE: Defining a Custom Edge Type in Svelte Flow
DESCRIPTION: Demonstrates how to define a custom edge type (`EdgeType`) by extending the base `Edge` type with specific data properties and a custom type identifier. Shows how to use this custom type within a Svelte component's `<script lang="ts">` block via `EdgeProps` and utilize helper functions like `getStraightPath` for rendering.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/advanced/typescript.mdx#_snippet_4

LANGUAGE: svelte
CODE:
```
<script module>
  export type EdgeType = Edge<{ value: number }, 'custom'>;
</script>

<script lang="ts">
  import { getStraightPath, BaseEdge, type EdgeProps } from '@xyflow/svelte';

  let { id, sourceX, sourceY, targetX, targetY }: EdgeProps<EdgeType> = $props();

  let [edgePath] = $derived(getStraightPath({ sourceX, sourceY, targetX, targetY }));
</script>

<BaseEdge {id} path={edgePath} />
```

----------------------------------------

TITLE: Implementing Color Preview with Custom Handles in React Flow
DESCRIPTION: This component utilizes the `CustomHandle` component to manage red, green, and blue color inputs. It maintains a local `color` state and updates it based on values received from the custom handles, dynamically setting the background color of the node.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/computing-flows.mdx#_snippet_2

LANGUAGE: jsx
CODE:
```
// {...}
function ColorPreview() {
  const [color, setColor] = useState({ r: 0, g: 0, b: 0 });

  return (
    <div
      className="node"
      style={{
        background: `rgb(${color.r}, ${color.g}, ${color.b})`,
      }}
    >
      <CustomHandle
        id="red"
        label="R"
        onChange={(value) => setColor((c) => ({ ...c, r: value }))}
      />
      <CustomHandle
        id="green"
        label="G"
        onChange={(value) => setColor((c) => ({ ...c, g: value }))}
      />
      <CustomHandle
        id="blue"
        label="B"
        onChange={(value) => setColor((c) => ({ ...c, b: value }))}
      />
    </div>
  );
}

export default ColorPreview;
```

----------------------------------------

TITLE: Using SvelteFlow Hooks with Typed Nodes and Edges (Svelte/TypeScript)
DESCRIPTION: Illustrates how to apply the custom `NodeType` and `EdgeType` generics to SvelteFlow hooks like `useSvelteFlow` and `useNodesData` within a Svelte component's script block. This ensures type-safe access to node and edge properties when using the hook return values.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/advanced/typescript.mdx#_snippet_6

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { useSvelteFlow, useNodeConnections, useNodesData } from '@xyflow/svelte';
  import type { NodeType, EdgeType } from './types';

  // Nodes and edges are now correctly typed
  const { getNodes, getEdges } = useSvelteFlow<NodeType, EdgeType>();

  const connections = useNodeConnections({
    handleType: 'target',
  });

  const nodesData = useNodesData<NodeType>(connections.current.map(c => c.source));

  $effect(() => {
    nodesData.current.forEach(({ type, data }) => {
      if (type === 'number') {
        // Type-safe access to number property
        console.log(data.number);
      }
    });
  });
</script>
```

----------------------------------------

TITLE: Defining DataEdge with Type Safety in xyflow (TypeScript)
DESCRIPTION: This snippet demonstrates how to define an `initialEdges` array using a custom `dataEdge` type in xyflow, ensuring type safety for the `key` property within the edge's data. It leverages TypeScript's `satisfies` predicate to validate that the `key` corresponds to a property of the `CounterNode`'s data, preventing invalid key assignments at compile time.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/components/edges/data-edge.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
type CounterNode = Node<{ count: number }>;

const initialEdges = [
  {
    id: 'edge-1',
    source: 'node-1',
    target: 'node-2',
    type: 'dataEdge',
    data: {
      key: 'count',
    } satisfies DataEdge<CounterNode>,
  },
];
```

----------------------------------------

TITLE: Setting Node Dimensions Directly (New API)
DESCRIPTION: This snippet shows the new approach in React Flow 12 for setting fixed node dimensions directly using `node.width` and `node.height`. These properties now act as inline styles, making it easier to pass dimensions, especially for server-side rendering.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_6

LANGUAGE: javascript
CODE:
```
// in React Flow 12 you can used node.width and node.height to set the dimensions
const nodes = [
  {
    id: '1',
    type: 'input',
    data: { label: 'input node' },
    position: { x: 250, y: 5 },
    width: 180,
    height: 40
  }
];
```

----------------------------------------

TITLE: Defining Node Types Outside Component (Recommended) - JSX
DESCRIPTION: This snippet shows the recommended approach for defining `nodeTypes` by placing the object outside the component. This ensures the `nodeTypes` object is created only once, preventing unnecessary re-renders of the React Flow instance and avoiding the associated warning.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_4

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
import MyCustomNode from './MyCustomNode';

// defined outside of the component
const nodeTypes = {
  myCustomNode: MyCustomNode,
};

function Flow(props) {
  return <ReactFlow nodeTypes={nodeTypes} />;
}

export default Flow;
```

----------------------------------------

TITLE: Defining OnNodesChange Type in TypeScript
DESCRIPTION: This snippet defines the `OnNodesChange` type, which is a function signature used for handling changes to nodes in React Flow. It accepts an array of `NodeChange` objects and returns void. It can be generic over a custom `NodeType`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/on-nodes-change.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type OnNodesChange<NodeType extends Node = Node> = (
  changes: NodeChange<NodeType>[],
) => void;
```

----------------------------------------

TITLE: Using OnNodesChange with useCallback in React Flow
DESCRIPTION: This example demonstrates how to implement the `onNodesChange` function using the `OnNodesChange` type and React's `useCallback` hook. It applies incoming node changes to the current state of nodes using `applyNodeChanges`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/on-nodes-change.mdx#_snippet_1

LANGUAGE: TypeScript
CODE:
```
const onNodesChange: OnNodesChange = useCallback(
  (changes) => setNodes((nds) => applyNodeChanges(changes, nds)),
  [setNodes],
);
```

----------------------------------------

TITLE: Implementing Custom Mindmap Node (React)
DESCRIPTION: Defines a custom React Flow node component (`MindMapNode`) for the mindmap tutorial. It includes a label input field, source and target handles, and a dedicated `dragHandle` div element used for dragging the node. It uses a Zustand store hook to update the node's label.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_20

LANGUAGE: tsx
CODE:
```
import { Handle, NodeProps, Position } from '@xyflow/react';

import useStore from '../store';

export type NodeData = {
  label: string;
};

function MindMapNode({ id, data }: NodeProps<NodeData>) {
  const updateNodeLabel = useStore((state) => state.updateNodeLabel);

  return (
    <>
      <div className="inputWrapper">
        <div className="dragHandle">
          {/* icon taken from grommet https://icons.grommet.io */}
          <svg viewBox="0 0 24 24">
            <path
              fill="#333"
              stroke="#333"
              strokeWidth="1"
              d="M15 5h2V3h-2v2zM7 5h2V3H7v2zm8 8h2v-2h-2v2zm-8 0h2v-2H7v2zm8 8h2v-2h-2v2zm-8 0h2v-2H7v2z"
            />
          </svg>
        </div>
        <input
          value={data.label}
          onChange={(evt) => updateNodeLabel(id, evt.target.value)}
          className="input"
        />
      </div>

      <Handle type="target" position={Position.Top} />
      <Handle type="source" position={Position.Top} />
    </>
  );
}

export default MindMapNode;
```

----------------------------------------

TITLE: Registering Custom Node with ReactFlow Component
DESCRIPTION: This snippet illustrates how to register a custom node, such as the `CounterNode`, with the `ReactFlow` component. By defining an object `nodeTypes` that maps a string key to the custom node component, you enable `ReactFlow` to render instances of your custom node when its type matches the key.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/node-props.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
import CounterNode from './CounterNode';

const nodeTypes = {
  counterNode: CounterNode,
};

export default function App() {
  return <ReactFlow nodeTypes={nodeTypes} ... />
}
```

----------------------------------------

TITLE: Implementing Sum Node Logic in React Flow
DESCRIPTION: This snippet defines the `SumNode` React component for a React Flow application. It calculates the sum of values from 'x' and 'y' target handles using `getHandleValue` and updates the node's data. It also renders the node's UI with input and output handles.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_19

LANGUAGE: TypeScript
CODE:
```
      getHandleConnections({ nodeId: id, id: 'x', type: 'target' }),
      state.nodeLookup,
    ),
    y: getHandleValue(
      getHandleConnections({ nodeId: id, id: 'y', type: 'target' }),
      state.nodeLookup,
    ),
  }));

  useEffect(() => {
    updateNodeData(id, { value: x + y });
  }, [x, y]);

  return (
    <BaseNode className="w-32">
      <NodeHeader>
        <NodeHeaderTitle>Sum</NodeHeaderTitle>
      </NodeHeader>

      <footer className="bg-gray-100 -m-5">
        <LabeledHandle title="x" id="x" type="target" position={Position.Left} />
        <LabeledHandle title="y" id="y" type="target" position={Position.Left} />
        <LabeledHandle title="out" type="source" position={Position.Right} />
      </footer>
    </BaseNode>
  );
}

function getHandleValue(
  connections: Array<{ source: string }>,
  lookup: Map<string, Node<any>>,
) {
  return connections.reduce((acc, { source }) => {
    const node = lookup.get(source)!;
    const value = node.data.value;

    return typeof value === 'number' ? acc + value : acc;
  }, 0);
}
```

----------------------------------------

TITLE: Defining Connection Points with Handle Component in Custom Nodes (JSX)
DESCRIPTION: This snippet demonstrates the basic usage of the `<Handle />` component within a custom React Flow node. It shows how to define a target handle on the left and a source handle on the right, enabling connections to and from the node. It requires `Handle` and `Position` from `@xyflow/react`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/handle.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { Handle, Position } from '@xyflow/react';

export const CustomNode = ({ data }) => {
  return (
    <>
      <div style={{ padding: '10px 20px' }}>
        {data.label}
      </div>

      <Handle type="target" position={Position.Left} />
      <Handle type="source" position={Position.Right} />
    </>
  );
};
```

----------------------------------------

TITLE: Subscribing to Node Count Changes with useStore (JSX)
DESCRIPTION: This snippet demonstrates how to use `useStore` to subscribe to changes in the number of nodes within a React Flow instance. It defines a selector function `nodesLengthSelector` to extract the `nodes.length` from the state and uses it to display the current node count, re-rendering the component whenever the count changes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-store.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { ReactFlow, useStore } from '@xyflow/react';

const nodesLengthSelector = (state) =>
  state.nodes.length || 0;

const NodesLengthDisplay = () => {
  const nodesLength = useStore(nodesLengthSelector);

  return <div>The current number of nodes is: {nodesLength}</div>;
};

function Flow() {
  return (
    <ReactFlow nodes={[...]}>
      <NodesLengthDisplay />
    </ReactFlow>
  );
}
```

----------------------------------------

TITLE: Applying Node Changes with Union Node Types in ReactFlow (v12)
DESCRIPTION: This snippet illustrates how to define the `onNodesChange` callback using the `AppNode` union type in ReactFlow v12. This ensures type safety when applying changes to a mixed array of node types.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_19

LANGUAGE: typescript
CODE:
```
const onNodesChange: onNodesChange<AppNode> = useCallback((changes) => setNodes(nds => applyChanges(changes, nds)), []);
```

----------------------------------------

TITLE: Rendering React Flow Without Parent Container Dimensions (Incorrect)
DESCRIPTION: This example illustrates an incorrect setup where the `ReactFlow` component is rendered within a `div` that lacks explicit width or height. React Flow requires its parent container to have defined dimensions to properly measure and display the graph, leading to a warning if not provided.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_9

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

function Flow(props) {
  return (
    <div>
      <ReactFlow {...props} />
    </div>
  );
}
```

----------------------------------------

TITLE: Using Union Node Types for Node Array Definition in ReactFlow (v12)
DESCRIPTION: This snippet demonstrates how to use the newly defined `AppNode` union type to create an array of nodes, allowing for different node types with specific data structures within the same collection in ReactFlow v12.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_18

LANGUAGE: typescript
CODE:
```
const nodes: AppNode[] = [
  { id: '1', type: 'number', data: { value: 1 }, position: { x: 100, y: 100 } },
  { id: '2', type: 'text', data: { text: 'Hello' }, position: { x: 200, y: 200 } },
];
```

----------------------------------------

TITLE: Generating Static HTML Output of Svelte Flow Diagram (Svelte)
DESCRIPTION: Provides a Svelte function `toHTML` that uses `svelte/server.render` to render the `SvelteFlow` component with specified nodes, edges, dimensions, and other props. It returns the resulting HTML string, suitable for static file generation or server responses.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/advanced/server-side-rendering.mdx#_snippet_3

LANGUAGE: svelte
CODE:
```
<script>
  import { SvelteFlow, Background } from '@xyflow/svelte';
  import { render } from 'svelte/server';

  function toHTML({ nodes, edges, width, height }) {
    const { html } = render(SvelteFlow, {
      props: {
        nodes,
        edges,
        width,
        height,
        minZoom: 0.2,
        fitView: true,
      },
      children: [Background],
    });

    return html;
  }
</script>
```

----------------------------------------

TITLE: Basic Svelte Flow Usage with TypeScript
DESCRIPTION: Demonstrates how to initialize a basic Svelte Flow instance using TypeScript types for nodes, edges, and options. Shows importing necessary components and types from `@xyflow/svelte` and defining initial state using `$state.raw` for type safety.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/advanced/typescript.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import {
    SvelteFlow,
    Controls,
    Background,
    BackgroundVariant,
    type Node,
    type Edge,
    type FitViewOptions,
    type DefaultEdgeOptions,
  } from '@xyflow/svelte';

  import '@xyflow/svelte/dist/style.css';

  let nodes = $state.raw<Node[]>([
    {
      id: '1',
      type: 'input',
      data: { label: 'Node 1' },
      position: { x: 5, y: 5 },
    },
    {
      id: '2',
      type: 'default',
      data: { label: 'Node 2' },
      position: { x: 5, y: 100 },
    },
  ]);

  let edges = $state.raw<Edge[]>([
    { id: 'e1-2', source: '1', target: '2' }
  ]);

  const fitViewOptions: FitViewOptions = {
    padding: 0.2,
  };

  const defaultEdgeOptions: DefaultEdgeOptions = {
    animated: true,
  };
</script>

<SvelteFlow
  bind:nodes
  bind:edges
  fitView
  {fitViewOptions}
  {defaultEdgeOptions}
>
  <Controls />
  <Background variant={BackgroundVariant.Dots} />
</SvelteFlow>
```

----------------------------------------

TITLE: Importing Svelte Flow Components and Styles (Svelte)
DESCRIPTION: Imports the main SvelteFlow component, the Background component, and the required CSS styles from the `@xyflow/svelte` package into a Svelte component script block.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/getting-started/building-a-flow.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script>
import { SvelteFlow, Background } from '@xyflow/svelte';
import '@xyflow/svelte/dist/style.css';
</script>
```

----------------------------------------

TITLE: Importing React Flow and TailwindCSS Styles (JavaScript)
DESCRIPTION: This snippet shows the recommended order for importing React Flow's default styles and TailwindCSS. It's crucial to import React Flow's styles first to allow Tailwind's utility classes to correctly override or augment them without unexpected conflicts.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/theming.mdx#_snippet_8

LANGUAGE: js
CODE:
```
import '@xyflow/react/dist/style.css';
import 'tailwind.css';
```

----------------------------------------

TITLE: Creating Custom Edge with BaseEdge in Svelte
DESCRIPTION: This snippet demonstrates how to create a custom Svelte edge component using the <BaseEdge /> component. It shows how to receive edge props, calculate the path using getBezierPath, and pass necessary props like path, markers, and labels to <BaseEdge />. Requires @xyflow/svelte.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/base-edge.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { BaseEdge, getBezierPath, type EdgeProps } from '@xyflow/svelte';

  let { sourceX, sourceY, targetX, targetY, ...props } : EdgeProps = $props();

  const [edgePath] = $derived(getBezierPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
  }));

  const {
    markerStart,
    markerEnd,
    interactionWidth,
    label,
    labelStyle,
  } = props;
</script>

<BaseEdge
  path={edgePath}
  {markerStart}
  {markerEnd}
  {interactionWidth}
  {label}
  {labelStyle}
/>
```

----------------------------------------

TITLE: Styling Custom React Flow Node with TailwindCSS (JSX)
DESCRIPTION: This snippet demonstrates styling a custom React Flow node using TailwindCSS utility classes. It applies various classes for layout, spacing, colors, and borders to create a visually distinct node, and also styles the Handle components for connections.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/theming.mdx#_snippet_7

LANGUAGE: jsx
CODE:
```
function CustomNode({ data }) {
  return (
    <div className="px-4 py-2 shadow-md rounded-md bg-white border-2 border-stone-400">
      <div className="flex">
        <div className="rounded-full w-12 h-12 flex justify-center items-center bg-gray-100">
          {data.emoji}
        </div>
        <div className="ml-2">
          <div className="text-lg font-bold">{data.name}</div>
          <div className="text-gray-500">{data.job}</div>
        </div>
      </div>

      <Handle
        type="target"
        position={Position.Top}
        className="w-16 !bg-teal-500"
      />
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-16 !bg-teal-500"
      />
    </div>
  );
}
```

----------------------------------------

TITLE: Updating Node Color in Zustand Store - TypeScript
DESCRIPTION: This TypeScript function defines an action within a Zustand store to update the data.color property of a specific node. It takes nodeId and color as parameters, iterates through the existing nodes, and returns a new node object with the updated color, ensuring React Flow detects the change.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/state-management.mdx#_snippet_1

LANGUAGE: typescript
CODE:
```
updateNodeColor: (nodeId: string, color: string) => {
  set({
    nodes: get().nodes.map((node) => {
      if (node.id === nodeId) {
        // it's important to create a new object here, to inform React Flow about the changes
        return { ...node, data: { ...node.data, color } };
      }

      return node;
    })
  });
};
```

----------------------------------------

TITLE: Disabling Canvas Pan with `nowheel` Class in React Flow (TSX)
DESCRIPTION: This snippet demonstrates how to apply the `nowheel` class to a custom node's container to prevent the React Flow canvas from panning when scrolling inside the node's content. It ensures that only the internal content scrolls, providing a better user experience for nodes with scrollable areas. The `overflow: 'auto'` style is used to enable scrolling within the div.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/custom-nodes.mdx#_snippet_5

LANGUAGE: tsx
CODE:
```
export default function CustomNode(props: NodeProps) {
  return (
    <div className="nowheel" style={{ overflow: 'auto' }}>
      <p>Scrollable content...</p>
    </div>
  );
}
```

----------------------------------------

TITLE: Setting Viewport with `useReactFlow` (New API) - JavaScript
DESCRIPTION: This snippet shows the new `setViewport` function from the `useReactFlow` hook, which replaces `transform`. It provides a consistent way to programmatically set the viewport's position and zoom level, aligning with other viewport functions.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_15

LANGUAGE: js
CODE:
```
const { setViewport, setCenter, setZoom } = useReactFlow();
...
setViewport({ x: 100, y: 100, zoom: 2 });
```

----------------------------------------

TITLE: Defining a Custom Sum Node in React Flow
DESCRIPTION: This partial `SumNode` component is intended to compute the sum of two input values within a React Flow diagram. It utilizes `BaseNode`, `LabeledHandle`, and `NodeHeader` for its structure and appearance, and leverages `useReactFlow` and `useStore` hooks for node data updates and state management.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_18

LANGUAGE: tsx
CODE:
```
import React, { useEffect } from 'react';
import {
  type Node,
  type NodeProps,
  Position,
  useReactFlow,
  useStore,
} from '@xyflow/react';

import { BaseNode } from '../base-node';
import { LabeledHandle } from '../labeled-handle';
import { NodeHeader, NodeHeaderTitle } from '../node-header';

export type SumNode = Node<{
  value: number;
}>;

export function SumNode({ id }: NodeProps<SumNode>) {
  const { updateNodeData, getHandleConnections } = useReactFlow();
  const { x, y } = useStore((state) => ({
    x: getHandleValue(
```

----------------------------------------

TITLE: Defining the Node Type in React Flow (TypeScript)
DESCRIPTION: This TypeScript snippet defines the `Node` type, which is the core data structure for all nodes in React Flow. It specifies properties like `id`, `position`, `data`, `type`, and various flags for interactivity (e.g., `draggable`, `selectable`), as well as styling options. Note that `width` and `height` are typically read-only and managed internally by React Flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/node.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type Node<
  NodeData extends Record<string, unknown> = Record<string, unknown>,
  NodeType extends string = string,
> = {
  id: string;
  position: XYPosition;
  data: NodeData;
  type?: NodeType;
  sourcePosition?: Position;
  targetPosition?: Position;
  hidden?: boolean;
  selected?: boolean;
  dragging?: boolean;
  draggable?: boolean;
  selectable?: boolean;
  connectable?: boolean;
  resizing?: boolean;
  deletable?: boolean;
  dragHandle?: string;
  width?: number | null;
  height?: number | null;
  parentId?: string;
  zIndex?: number;
  extent?: 'parent' | CoordinateExtent;
  expandParent?: boolean;
  ariaLabel?: string;
  focusable?: boolean;
  style?: React.CSSProperties;
  className?: string;
  origin?: NodeOrigin;
  handles?: NodeHandle[];
  measured?: {
    width?: number;
    height?: number;
  };
};
```

----------------------------------------

TITLE: Creating a Basic Svelte Flow
DESCRIPTION: This snippet demonstrates how to initialize a basic Svelte Flow instance. It imports the necessary components, defines initial nodes and edges, and renders the flow within a container element. It includes Controls and Background components for basic interaction and visualization.
SOURCE: https://github.com/xyflow/web/blob/main/apps/example-apps/svelte/examples/misc/hello-world/README.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script>
  import SvelteFlow, { Controls, Background } from '@xyflow/svelte';
  import '@xyflow/svelte/dist/style.css';

  const initialNodes = [
    { id: '1', position: { x: 0, y: 0 }, data: { label: 'Node 1' } },
    { id: '2', position: { x: 100, y: 100 }, data: { label: 'Node 2' } }
  ];

  const initialEdges = [
    { id: 'e1-2', source: '1', target: '2' }
  ];

  let nodes = initialNodes;
  let edges = initialEdges;
</script>

<div style="width: 100%; height: 500px;">
  <SvelteFlow {nodes} {edges}>
    <Controls />
    <Background />
  </SvelteFlow>
</div>

<style>
  .svelte-flow {
    border: 1px solid #eee;
  }
</style>
```

----------------------------------------

TITLE: Instantiating a Custom Node Type in React Flow
DESCRIPTION: This example illustrates how to instantiate a custom node within the `nodes` array. By setting the `type` property to `'textUpdater'`, React Flow knows to render the `TextUpdaterNode` component for this specific node, allowing for custom data and positioning.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/custom-nodes.mdx#_snippet_3

LANGUAGE: js
CODE:
```
const nodes = [
  {
    id: 'node-1',
    type: 'textUpdater',
    position: { x: 0, y: 0 },
    data: { value: 123 },
  },
];
```

----------------------------------------

TITLE: Memoizing Node Types with useMemo (JSX)
DESCRIPTION: This snippet demonstrates the correct way to pass `nodeTypes` to `ReactFlow` by memoizing them using the `useMemo` hook. This ensures that the `nodeTypes` object is not re-created on every render, preventing performance issues and ensuring proper component behavior.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
function Flow() {
  const nodeTypes = useMemo(() => ({ specialType: SpecialNode }), []);

  return <ReactFlow nodes={[]} nodeTypes={nodeTypes} />;
}
```

----------------------------------------

TITLE: Integrating TooltipNode into React Flow App (TSX)
DESCRIPTION: This TSX code updates `App.tsx` to set up a basic React Flow instance and integrate the `<TooltipNode />` component. It defines `nodeTypes` to map 'tooltip' to the `Tooltip` component and initializes a single node of this type, demonstrating a complete React Flow setup with a custom component.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_15

LANGUAGE: tsx
CODE:
```
import '@xyflow/react/dist/style.css';

import { ReactFlow, Position, useNodesState, Node } from '@xyflow/react';

import { TooltipNode, TooltipContent, TooltipTrigger } from '@/components/tooltip-node';

function Tooltip() {
  return (
    <TooltipNode>
      <TooltipContent position={Position.Top}>Hidden Content</TooltipContent>
      <TooltipTrigger>Hover</TooltipTrigger>
    </TooltipNode>
  );
}

const nodeTypes = {
  tooltip: Tooltip,
};

const initialNodes: Node[] = [
  {
    id: '1',
    position: { x: 0, y: 0 },
    data: {},
    type: 'tooltip',
  },
];

function App() {
  const [nodes, , onNodesChange] = useNodesState(initialNodes);

  return (
    <div className="h-screen w-screen p-8">
      <ReactFlow
        nodes={nodes}
        onNodesChange={onNodesChange}
        nodeTypes={nodeTypes}
        fitView
      />
    </div>
  );
}

export default App;
```

----------------------------------------

TITLE: Implementing a Custom Node with NodeToolbar in React
DESCRIPTION: This snippet demonstrates how to integrate the NodeToolbar component into a custom React Flow node. It imports necessary components like Handle, Position, and NodeToolbar from @xyflow/react. The toolbar's visibility and position are controlled by data.toolbarVisible and data.toolbarPosition props, allowing dynamic control. It also includes basic Handle components for node connections.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/node-toolbar.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { memo } from 'react';
import { Handle, Position, NodeToolbar } from '@xyflow/react';

const CustomNode = ({ data }) => {
  return (
    <>
      <NodeToolbar isVisible={data.toolbarVisible} position={data.toolbarPosition}>
        <button>delete</button>
        <button>copy</button>
        <button>expand</button>
      </NodeToolbar>

      <div style={{ padding: '10px 20px' }}>
        {data.label}
      </div>

      <Handle type="target" position={Position.Left} />
      <Handle type="source" position={Position.Right} />
    </>
  );
};

export default memo(CustomNode);
```

----------------------------------------

TITLE: Correctly Applying Parent Extent to a Child Node in React Flow
DESCRIPTION: This snippet shows the correct implementation for using `extent: 'parent'` by also specifying a `parentNode`. This ensures the node is recognized as a child and its movement is correctly constrained within the bounds of its designated parent node.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_12

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
const nodes = [
  {
    id: 'mycustomnode',
    parentNode: 'someothernode',
    extent: 'parent',
    // ...
  },
];

function Flow(props) {
  return <ReactFlow nodes={nodes} />;
}
```

----------------------------------------

TITLE: Adding a Label and Delete Button to a Custom Edge in Svelte Flow
DESCRIPTION: This snippet enhances the custom edge by adding an interactive label using the EdgeLabel component. The label is positioned using coordinates calculated by getStraightPath. It includes a button with 'nodrag' and 'nopan' classes to prevent canvas interaction, which uses the useEdges hook to access and update the edges store, allowing the edge to be deleted when clicked.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/customization/custom-edges.mdx#_snippet_2

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import {
    BaseEdge,
    EdgeLabel,
    getStraightPath,
    useEdges,
    type EdgeProps,
  } from '@xyflow/svelte';

  let { id, sourceX, sourceY, targetX, targetY }: EdgeProps = $props();

  let [edgePath, labelX, labelY] = $derived(
    getStraightPath({
      sourceX,
      sourceY,
      targetX,
      targetY,
    })
  );

  const edges = useEdges();
</script>

<BaseEdge {id} path={edgePath} />
<EdgeLabel x={labelX} y={labelY}>
  <button
    class="nodrag nopan"
    onclick={() => {
      edges.update((eds) => eds.filter((edge) => edge.id !== id));
    }}
  >
    delete
  </button>
</EdgeLabel>
```

----------------------------------------

TITLE: Setting up ReactFlow Panel and Store Selector in JSX
DESCRIPTION: This React component snippet demonstrates importing `ReactFlow` and `Panel` from `@xyflow/react`. It defines a selector to extract the `createNode` action from the Zustand store, then renders a `ReactFlow` instance with a `Panel` positioned at the top-right, ready for UI controls.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_31

LANGUAGE: jsx
CODE:
```
...
import { ReactFlow, Panel } from '@xyflow/react';
...

const selector = (store) => ({
  ...,
  createNode: store.createNode,
});

export default function App() {
  const store = useStore(selector, shallow);

  return (
    <ReactFlow>
      <Panel position="top-right">
        ...
      </Panel>
      <Background />
    </ReactFlow>
  );
};
```

----------------------------------------

TITLE: Defining NodeProps Type in TypeScript
DESCRIPTION: This TypeScript type definition outlines the properties that are automatically passed to any custom node component in xyflow. It includes essential identifiers like `id` and `data`, positional information such as `positionAbsoluteX` and `positionAbsoluteY`, and state flags like `selected` and `dragging`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/node-props.mdx#_snippet_0

LANGUAGE: ts
CODE:
```
export type NodeProps<NodeType extends Node = Node> = {
  id: string;
  data: Node['data'];
  dragHandle?: boolean;
  type?: string;
  selected?: boolean;
  isConnectable?: boolean;
  zIndex?: number;
  positionAbsoluteX: number;
  positionAbsoluteY: number;
  dragging: boolean;
  targetPosition?: Position;
  sourcePosition?: Position;
};
```

----------------------------------------

TITLE: Calling Node Color Update Action in React Component - JSX
DESCRIPTION: This React JSX snippet demonstrates how to access and use the updateNodeColor action from the Zustand store within a React component. It retrieves the action using the useStore hook and then attaches it to a button's onClick event handler to trigger a node color update.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/state-management.mdx#_snippet_2

LANGUAGE: jsx
CODE:
```
const updateNodeColor = useStore((s) => s.updateNodeColor);
...
<button onClick={() => updateNodeColor(nodeId, color)} />;
```

----------------------------------------

TITLE: Define State with $state.raw in Svelte Flow v1 (New API)
DESCRIPTION: Demonstrates the new approach for defining `nodes` and `edges` state in Svelte Flow 1.0 using Svelte 5's `$state.raw` rune, reflecting the shift towards immutability.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_1

LANGUAGE: javascript
CODE:
```
let nodes = $state.raw([...]);
let edges = $state.raw([...]);
```

----------------------------------------

TITLE: Initializing Edges Array with $state.raw (JavaScript)
DESCRIPTION: Initializes a reactive array of edge objects using Svelte's `$state.raw` rune. Each edge requires an `id`, `source` (ID of the source node), and `target` (ID of the target node) to connect two nodes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/getting-started/building-a-flow.mdx#_snippet_4

LANGUAGE: javascript
CODE:
```
let edges = $state.raw([{ id: 'e1-2', source: '1', target: '2' }]);
```

----------------------------------------

TITLE: Defining ConnectionLineComponentProps Interface in React Flow
DESCRIPTION: This TypeScript type definition outlines the properties passed to a custom connection line component in React Flow. It includes styling, connection type, source and target node/handle details, coordinates, positions, and connection status, enabling developers to create custom visual representations for connections.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/connection-line-component-props.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type ConnectionLineComponentProps = {
  connectionLineStyle?: React.CSSProperties;
  connectionLineType: ConnectionLineType;
  fromNode?: Node;
  fromHandle?: Handle;
  fromX: number;
  fromY: number;
  toX: number;
  toY: number;
  fromPosition: Position;
  toPosition: Position;
  connectionStatus: 'valid' | 'invalid' | null;
};
```

----------------------------------------

TITLE: Creating a Custom Handle Component in React Flow
DESCRIPTION: This component uses `useNodeConnections` to find connected nodes and `useNodesData` to fetch data from the first connected source node. It updates its internal state via `onChange` when the connected node's data changes, providing a reusable way to bind data to handles.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/computing-flows.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
// {...}
function CustomHandle({ id, label, onChange }) {
  const connections = useNodeConnections({
    handleType: 'target',
    handleId: id,
  });

  const nodeData = useNodesData(connections?.[0].source);

  useEffect(() => {
    onChange(nodeData?.data ? nodeData.data.value : 0);
  }, [nodeData]);

  return (
    <div>
      <Handle
        type="target"
        position={Position.Left}
        id={id}
        className="handle"
      />
      <label htmlFor="red" className="label">
        {label}
      </label>
    </div>
  );
}
```

----------------------------------------

TITLE: Creating a Number Input Custom Node (React)
DESCRIPTION: This React component defines a custom node for React Flow, `NumberInput`, which provides a controlled number input field. It limits input values to integers between 0 and 255 and includes a source handle for connecting to other nodes. The component uses `useState` for local state management and `useCallback` for the `onChange` handler.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/computing-flows.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useCallback, useState } from 'react';
import { Handle, Position } from '@xyflow/react';

function NumberInput({ id, data }) {
  const [number, setNumber] = useState(0);

  const onChange = useCallback((evt) => {
    const cappedNumber = Math.round(
      Math.min(255, Math.max(0, evt.target.value)),
    );
    setNumber(cappedNumber);
  }, []);

  return (
    <div className="number-input">
      <div>{data.label}</div>
      <input
        id={`number-${id}`}
        name="number"
        type="number"
        min="0"
        max="255"
        onChange={onChange}
        className="nodrag"
        value={number}
      />
      <Handle type="source" position={Position.Right} />
    </div>
  );
}

export default NumberInput;
```

----------------------------------------

TITLE: Registering a Custom Edge Type with ReactFlow
DESCRIPTION: This example demonstrates how to register a custom edge component, `CustomEdge`, with the `ReactFlow` component. The `edgeTypes` object maps a string identifier ('custom-edge') to the custom component, which is then passed to the `<ReactFlow />` component's `edgeTypes` prop. It's crucial to define `edgeTypes` outside the component or use `useMemo` to optimize performance and prevent unnecessary re-renders.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/custom-edges.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
import ReactFlow from '@xyflow/react'
import CustomEdge from './CustomEdge'


const edgeTypes = {
  'custom-edge': CustomEdge
}

export function Flow() {
  return <ReactFlow edgeTypes={edgeTypes} ... />
}
```

----------------------------------------

TITLE: Positioning Content with Panel in ReactFlow (JSX)
DESCRIPTION: This snippet demonstrates how to use the <Panel /> component within a <ReactFlow /> instance to position various content elements at different predefined locations (top-left, top-center, top-right, bottom-left, bottom-center, bottom-right) above the viewport. It showcases the `position` prop for precise placement and requires `@xyflow/react` as a dependency.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/panel.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { ReactFlow, Panel } from '@xyflow/react';

export default function Flow() {
  return (
    <ReactFlow nodes={[...]} fitView>
      <Panel position="top-left">top-left</Panel>
      <Panel position="top-center">top-center</Panel>
      <Panel position="top-right">top-right</Panel>
      <Panel position="bottom-left">bottom-left</Panel>
      <Panel position="bottom-center">bottom-center</Panel>
      <Panel position="bottom-right">bottom-right</Panel>
    </ReactFlow>
  );
}
```

----------------------------------------

TITLE: Using NodeToolbar in a Svelte Custom Node
DESCRIPTION: This snippet demonstrates how to integrate the NodeToolbar component into a custom Svelte node. It imports the necessary components and types, defines node props, and places the NodeToolbar within the node's template, containing simple buttons.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/node-toolbar.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
	import { NodeToolbar, Handle, Position, type NodeProps } from '@xyflow/svelte';

	let { data } : NodeProps = $props();
</script>

<NodeToolbar>
	<button>delete</button>
	<button>copy</button>
	<button>expand</button>
</NodeToolbar>

<div>{data.label}</div>
<Handle type="target" position={Position.Left} />
<Handle type="source" position={Position.Right} />
```

----------------------------------------

TITLE: Applying `fitView` with Explicit Container Dimensions in React Flow (JSX)
DESCRIPTION: This snippet demonstrates how to use the `fitView` prop on the `ReactFlow` component when performing server-side rendering. By explicitly passing the `width` and `height` of the container, React Flow can calculate the appropriate viewport `transform` on the server to ensure all nodes are visible.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/ssr-ssg-configuration.mdx#_snippet_2

LANGUAGE: JSX
CODE:
```
<ReactFlow nodes={nodes} edges={edges} fitView width={1000} height={500} />
```

----------------------------------------

TITLE: Using Panel component in SvelteFlow
DESCRIPTION: This snippet demonstrates how to import and use the <Panel /> component within a <SvelteFlow /> instance. It shows multiple <Panel /> components positioned at different corners and edges of the viewport.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/panel.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { SvelteFlow, Panel } from '@xyflow/svelte';
  import '@xyflow/svelte/dist/style.css';


  let nodes = $state.raw([]);
  let edges = $state.raw([]);
</script>

<SvelteFlow bind:nodes bind:edges>
  <Panel position="top-left">top-left</Panel>
  <Panel position="top-center">top-center</Panel>
  <Panel position="top-right">top-right</Panel>
  <Panel position="bottom-left">bottom-left</Panel>
  <Panel position="bottom-center">bottom-center</Panel>
  <Panel position="bottom-right">bottom-right</Panel>
</SvelteFlow>
```

----------------------------------------

TITLE: Update Nodes Immutably in Svelte Flow v1 (New API)
DESCRIPTION: Provides examples of how to correctly update node properties in Svelte Flow 1.0, emphasizing the need to treat `nodes` as immutable by creating new objects and reassigning the array. Includes examples using array methods and the `updateNode` helper.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_2

LANGUAGE: javascript
CODE:
```
nodes[0].position.x = 100; // won't work

const newNode = { ...nodes[0] };
newNode.position.x = 100;
nodes[0] = newNode; // not enough to trigger an update
nodes = [...nodes]; // this will make it work

nodes = nodes.map((node) => {
  if (node.id === '1') {
    return { ...node, position: { ...node.position, x: 100 } };
  }
  return node;
}); // also works

updateNode('1', (node) => ({
  ...node,
  position: { ...node.position, x: 100 },
})); // using the updateNode helper from useSvelteFlow
```

----------------------------------------

TITLE: Update Node Label in Zustand Store (TS)
DESCRIPTION: Zustand store action `updateNodeLabel` that takes a node ID and a new label string. It iterates through the existing nodes, finds the node with the matching ID, and updates its `data.label` property immutably by creating a new data object. This ensures React Flow is notified of the change.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_16

LANGUAGE: ts
CODE:
```
updateNodeLabel: (nodeId: string, label: string) => {
  set({
    nodes: get().nodes.map((node) => {
      if (node.id === nodeId) {
        // it's important to create a new object here, to inform React Flow about the changes
        node.data = { ...node.data, label };
      }

      return node;
    }),
  });
},
```

----------------------------------------

TITLE: Applying Custom Node Types to MiniMap in TypeScript
DESCRIPTION: This snippet demonstrates how to specify a custom node type, CustomNodeType, as a generic argument for the <MiniMap /> component in TypeScript. This ensures type safety and proper autocompletion when working with custom node structures within the ReactFlow application.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/minimap.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
<MiniMap<CustomNodeType> nodeColor={nodeColor} />
```

----------------------------------------

TITLE: Using Svelte Flow fitView with Explicit Dimensions for SSR (Svelte)
DESCRIPTION: Shows how to pass explicit `width` and `height` props to the `SvelteFlow` component when using the `fitView` prop during server-side rendering. This allows `fitView` to calculate the correct viewport transformation without relying on browser layout.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/advanced/server-side-rendering.mdx#_snippet_2

LANGUAGE: svelte
CODE:
```
<script>
  import { SvelteFlow } from '@xyflow/svelte';

  let nodes = $state.raw([/* ... */]);
  let edges = $state.raw([/* ... */]);
</script>

<SvelteFlow {nodes} {edges} fitView width={1000} height={500} />
```

----------------------------------------

TITLE: Basic Svelte Flow Component (Svelte)
DESCRIPTION: Example Svelte component demonstrating the basic setup for a Svelte Flow diagram. It imports the main component and styles, defines initial nodes and edges using $state.raw for performance, and renders the <SvelteFlow /> component within a sized container.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/index.mdx#_snippet_4

LANGUAGE: svelte
CODE:
```
<script>
  import { SvelteFlow } from '@xyflow/svelte';

  import '@xyflow/svelte/dist/style.css';

  let nodes = $state.raw([
    { id: '1', position: { x: 0, y: 0 }, data: { label: '1' } },
    { id: '2', position: { x: 0, y: 100 }, data: { label: '2' } },
  ]);

  let edges = $state.raw([
    { id: 'e1-2', source: '1', target: '2' },
  ]);
</script>

<div style:width="100vh" style:height="100vh">
  <SvelteFlow bind:nodes bind:edges />
</div>
```

----------------------------------------

TITLE: Binding Nodes to Svelte Flow (Svelte)
DESCRIPTION: Binds the `nodes` array initialized in the script block to the `SvelteFlow` component using Svelte's `bind:` directive. This enables two-way data binding, allowing both the component and the script to update the nodes array.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/getting-started/building-a-flow.mdx#_snippet_3

LANGUAGE: svelte
CODE:
```
<SvelteFlow bind:nodes>
```

----------------------------------------

TITLE: Creating a Custom Edge with Delete Button in React Flow (JSX)
DESCRIPTION: This snippet defines a `CustomEdge` component that renders a `BaseEdge` and an interactive delete button as an edge label. It utilizes `EdgeLabelRenderer` to portal the button for performance and `getStraightPath` to define the edge's visual path. Clicking the button removes the associated edge from the flow by updating the `edges` state.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/custom-edges.mdx#_snippet_2

LANGUAGE: jsx
CODE:
```
import {
  BaseEdge,
  EdgeLabelRenderer,
  getStraightPath,
  useReactFlow,
} from '@xyflow/react';

export default function CustomEdge({ id, sourceX, sourceY, targetX, targetY }) {
  const { setEdges } = useReactFlow();
  const [edgePath] = getStraightPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
  });

  return (
    <>
      <BaseEdge id={id} path={edgePath} />
      <EdgeLabelRenderer>
        <button
          onClick={() => setEdges((edges) => edges.filter((e) => e.id !== id))}
        >
          delete
        </button>
      </EdgeLabelRenderer>
    </>
  );
}
```

----------------------------------------

TITLE: Initializing Svelte Flow with Nodes and Edges (Svelte)
DESCRIPTION: This snippet demonstrates how to set up a basic Svelte Flow instance. It imports necessary components, defines initial nodes and edges using Svelte's `writable` stores, imports the default styles, and renders the main `SvelteFlow` component along with built-in controls, background, and minimap.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/src/content/svelte-flow-launch.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { writable } from 'svelte/store';
  import {
    SvelteFlow,
    Controls,
    Background,
    MiniMap,
  } from '@xyflow/svelte';

  import '@xyflow/svelte/dist/style.css'

  const nodes = writable([
    {
      id: '1',
      data: { label: 'Hello' },
      position: { x: 0, y: 0 }
    },
    {
      id: '2',
      data: { label: 'World' },
      position: { x: 100, y: 100 }
    }
  ]);

  const edges = writable([
    {
      id: '1-2',
      source: '1',
      target: '2',
    }
  ]);
</script>

<SvelteFlow {nodes} {edges} fitView>
  <Controls />
  <Background />
  <MiniMap />
</SvelteFlow>
```

----------------------------------------

TITLE: Initializing Zustand Store for React Flow in JavaScript
DESCRIPTION: This snippet defines a Zustand store (`useStore`) for managing React Flow's nodes and edges. It includes actions `onNodesChange` and `onEdgesChange` for applying updates from React Flow's helper functions, and `addEdge` for creating new edges with a unique ID. Dependencies include `@xyflow/react` for change application and `nanoid` for ID generation.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_7

LANGUAGE: javascript
CODE:
```
import { applyNodeChanges, applyEdgeChanges } from '@xyflow/react';
import { nanoid } from 'nanoid';
import { createWithEqualityFn } from 'zustand/traditional';

export const useStore = createWithEqualityFn((set, get) => ({
  nodes: [],
  edges: [],

  onNodesChange(changes) {
    set({
      nodes: applyNodeChanges(changes, get().nodes),
    });
  },

  onEdgesChange(changes) {
    set({
      edges: applyEdgeChanges(changes, get().edges),
    });
  },

  addEdge(data) {
    const id = nanoid(6);
    const edge = { id, ...data };

    set({ edges: [edge, ...get().edges] });
  },
}));
```

----------------------------------------

TITLE: Defining a Custom Slide Node Component for React Flow
DESCRIPTION: This `Slide.tsx` file defines a custom React Flow node component named `Slide`. It sets constant dimensions for the slide, defines types for the node and its data, and renders a basic `article` element with a 'Hello, React Flow!' message, serving as a placeholder for presentation slides.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
import { type Node, type NodeProps } from '@xyflow/react';

export const SLIDE_WIDTH = 1920;
export const SLIDE_HEIGHT = 1080;

export type SlideNode = Node<SlideData, 'slide'>;

export type SlideData = {};

const style = {
  width: `${SLIDE_WIDTH}px`,
  height: `${SLIDE_HEIGHT}px`,
} satisfies React.CSSProperties;

export function Slide({ data }: NodeProps<SlideNode>) {
  return (
    <article className="slide nodrag" style={style}>
      <div>Hello, React Flow!</div>
    </article>
  );
}
```

----------------------------------------

TITLE: Initializing React Flow with Markdown Slide Nodes (TSX)
DESCRIPTION: This snippet demonstrates how to set up a React Flow application with custom `slide` nodes. It registers the `Slide` component as a `nodeType` and defines an array of nodes, each with a `type: 'slide'` and `data.source` containing Markdown content. The `minZoom` prop is added to the `ReactFlow` component to allow viewing multiple large slides simultaneously.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_6

LANGUAGE: tsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
import { Slide, SLIDE_WIDTH } from './Slide';

const nodeTypes = {
  slide: Slide,
};

export default function App() {
  const nodes = [
    {
      id: '0',
      type: 'slide',
      position: { x: 0, y: 0 },
      data: { source: '# Hello, React Flow!' },
    },
    {
      id: '1',
      type: 'slide',
      position: { x: SLIDE_WIDTH, y: 0 },
      data: { source: '...' },
    },
    {
      id: '2',
      type: 'slide',
      position: { x: SLIDE_WIDTH * 2, y: 0 },
      data: { source: '...' },
    },
  ];

  return <ReactFlow nodes={nodes} nodeTypes={nodeTypes} fitView minZoom={0.1} />;
}
```

----------------------------------------

TITLE: Correctly Registering Custom Node Types in React Flow
DESCRIPTION: This snippet demonstrates the correct way to register and use custom node types in React Flow. The `node.type` ('custom') exactly matches the key in the `nodeTypes` object, allowing React Flow to correctly render `MyCustomNode`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_8

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import MyCustomNode from './MyCustomNode';

const nodes = [
  {
    id: 'mycustomnode',
    type: 'custom',
    // ...
  },
];

const nodeTypes = {
  custom: MyCustomNode,
};

function Flow(props) {
  return <ReactFlow nodes={nodes} nodeTypes={nodeTypes} />;
}
```

----------------------------------------

TITLE: Registering a Custom Edge Type in Svelte Flow App
DESCRIPTION: This snippet demonstrates how to register a custom edge component with the SvelteFlow component. The custom edge component is mapped to a string identifier ('custom-edge' in this case) within the edgeTypes object, which is then passed to the SvelteFlow component. It's important to define edgeTypes outside the component or use $derived to prevent unnecessary re-renders.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/customization/custom-edges.mdx#_snippet_1

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { SvelteFlow, type EdgeTypes } from '@xyflow/svelte';
  import CustomEdge from './CustomEdge.svelte';

  const edgeTypes: EdgeTypes = {
    'custom-edge': CustomEdge
  };
</script>

<SvelteFlow {edgeTypes} ... />
```

----------------------------------------

TITLE: Defining Connection Line Types Enum in ReactFlow
DESCRIPTION: This TypeScript enum, `ConnectionLineType`, defines the available styles for connection lines in a ReactFlow component. Each member corresponds to a specific visual rendering of the connection line, such as Bezier, Straight, Step, SmoothStep, and SimpleBezier, which can be applied via the `connectionLineType` prop.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/connection-line-type.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
export enum ConnectionLineType {
  Bezier = 'default',
  Straight = 'straight',
  Step = 'step',
  SmoothStep = 'smoothstep',
  SimpleBezier = 'simplebezier'
}
```

----------------------------------------

TITLE: Importing and Using useConnection Hook in Svelte
DESCRIPTION: This snippet demonstrates how to import the useConnection hook from the @xyflow/svelte library and call it within a Svelte script block to get the current connection state.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/hooks/use-connection.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { useConnection } from '@xyflow/svelte';

  const connection = useConnection();
</script>
```

----------------------------------------

TITLE: Rendering a Basic Background with Dots in ReactFlow (JSX)
DESCRIPTION: This snippet demonstrates how to integrate the Background component into a ReactFlow instance. It sets a dotted background with a light grey color, providing a common visual element for node-based UIs. It requires @xyflow/react as a dependency.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/background.mdx#_snippet_0

LANGUAGE: JSX
CODE:
```
import { useState } from 'react';
import { ReactFlow, Background, BackgroundVariant } from '@xyflow/react';

export default function Flow() {
  return (
    <ReactFlow defaultNodes={[...]} defaultEdges={[...]}>
      <Background color="#ccc" variant={BackgroundVariant.Dots} />
    </ReactFlow>
  );
}
```

----------------------------------------

TITLE: Using useNodeId() in a Custom Node (JavaScript)
DESCRIPTION: This example demonstrates how to use the `useNodeId` hook within a custom React Flow node. The `CustomNode` component renders a `NodeIdDisplay` child, which then uses `useNodeId` to retrieve and display the ID of the parent node without needing to pass the ID as a prop. This hook is intended for use within custom nodes or their descendants.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-node-id.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { useNodeId } from '@xyflow/react';

export default function CustomNode() {
  return (
    <div>
      <span>This node has an id of </span>
      <NodeIdDisplay />
    </div>
  );
}

function NodeIdDisplay() {
  const nodeId = useNodeId();

  return <span>{nodeId}</span>;
}
```

----------------------------------------

TITLE: Using BaseEdge in a Custom Edge Component (JSX)
DESCRIPTION: This snippet demonstrates how to create a custom edge component using <BaseEdge />. It calculates a straight path between source and target points and passes it, along with label and marker props, to the <BaseEdge /> component for rendering.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/base-edge.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { BaseEdge } from '@xyflow/react';

export function CustomEdge({ sourceX, sourceY, targetX, targetY, ...props }) {
  const [edgePath] = getStraightPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
  });

  const { label, labelStyle, markerStart, markerEnd, interactionWidth } = props;

  return (
    <BaseEdge
      path={edgePath}
      label={label}
      labelStyle={labelStyle}
      markerEnd={markerEnd}
      markerStart={markerStart}
      interactionWidth={interactionWidth}
    />
  );
}
```

----------------------------------------

TITLE: Implementing Resizable Svelte Node with NodeResizer
DESCRIPTION: This Svelte code demonstrates how to create a custom node component that includes the <NodeResizer /> component from the @xyflow/svelte package. The resizer is conditionally rendered when the node is selected, allowing users to change the node's size.
SOURCE: https://github.com/xyflow/web/blob/main/apps/example-apps/svelte/examples/nodes/node-resizer/README.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script>
  import { NodeResizer } from '@xyflow/svelte';
  import { writable } from 'svelte/store';

  export let data;
  export let id;
  export let selected;

  // You might manage size state here if needed,
  // but NodeResizer often handles the visual aspect.
  // Let's just show the basic usage.
</script>

<div class="custom-node" style="width: {data.width || 100}px; height: {data.height || 100}px;">
  {data.label || 'Resizable Node'}

  {#if selected}
    <NodeResizer minWidth={50} minHeight={50} />
  {/if}
</div>

<style>
  .custom-node {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative; /* Needed for NodeResizer positioning */
  }
</style>
```

----------------------------------------

TITLE: Integrating Custom Node into React Flow Application (React/JSX)
DESCRIPTION: This snippet shows the beginning of integrating custom nodes into the main React Flow application. It imports the `Osc` component and prepares for defining `nodeTypes`, which will map custom node types to their corresponding React components for rendering within React Flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_13

LANGUAGE: jsx
CODE:
```
import React from 'react';
import { ReactFlow } from '@xyflow/react';
import { shallow } from 'zustand/shallow';

import { useStore } from './store';
import Osc from './nodes/Osc';

const selector = (store) => ({
  nodes: store.nodes,
  edges: store.edges,
  onNodesChange: store.onNodesChange,
  onEdgesChange: store.onEdgesChange,
  addEdge: store.addEdge,
```

----------------------------------------

TITLE: Defining EdgeMarker Type in TypeScript
DESCRIPTION: This TypeScript type definition specifies the structure for configuring edge markers in xyflow. It includes properties for the marker's type, color, dimensions (width, height), and SVG-related attributes like `markerUnits`, `orient`, and `strokeWidth`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/edge-marker.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type EdgeMarker = {
  type: MarkerType;
  color?: string;
  width?: number;
  height?: number;
  markerUnits?: string;
  orient?: string;
  strokeWidth?: number;
};
```

----------------------------------------

TITLE: Mocking DOM APIs for Jest Testing in React Flow (TypeScript)
DESCRIPTION: This TypeScript code provides necessary mocks for `ResizeObserver`, `DOMMatrixReadOnly`, `HTMLElement.offsetHeight/offsetWidth`, and `SVGElement.getBBox` to enable Jest testing of React Flow applications. React Flow relies on these DOM APIs for node measurement and rendering, which are not available in a standard Jest environment. Calling `mockReactFlow()` initializes these global overrides.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/testing.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
// To make sure that the tests are working, it's important that you are using\n// this implementation of ResizeObserver and DOMMatrixReadOnly\nclass ResizeObserver {\n  callback: globalThis.ResizeObserverCallback;\n\n  constructor(callback: globalThis.ResizeObserverCallback) {\n    this.callback = callback;\n  }\n\n  observe(target: Element) {\n    this.callback([{ target } as globalThis.ResizeObserverEntry], this);\n  }\n\n  unobserve() {}\n\n  disconnect() {}\n}\n\nclass DOMMatrixReadOnly {\n  m22: number;\n  constructor(transform: string) {\n    const scale = transform?.match(/scale\(([1-9.])\)/)?.[1];\n    this.m22 = scale !== undefined ? +scale : 1;\n  }\n}\n\n// Only run the shim once when requested\nlet init = false;\n\nexport const mockReactFlow = () => {\n  if (init) return;\n  init = true;\n\n  global.ResizeObserver = ResizeObserver;\n\n  // @ts-ignore\n  global.DOMMatrixReadOnly = DOMMatrixReadOnly;\n\n  Object.defineProperties(global.HTMLElement.prototype, {\n    offsetHeight: {\n      get() {\n        return parseFloat(this.style.height) || 1;\n      },\n    },\n    offsetWidth: {\n      get() {\n        return parseFloat(this.style.width) || 1;\n      },\n    },\n  });\n\n  (global.SVGElement as any).prototype.getBBox = () => ({\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0,\n  });\n};
```

----------------------------------------

TITLE: Adding Node Type to React Flow (Outside Component)
DESCRIPTION: This example shows how to register a custom node component (`TextUpdaterNode`) with React Flow by defining the `nodeTypes` object outside of the main component. This approach prevents unnecessary re-renders and ensures optimal performance for the `ReactFlow` component.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/custom-nodes.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
const nodeTypes = {
  textUpdater: TextUpdaterNode
};

function Flow() {
  ...
  return (
    <ReactFlow
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onConnect={onConnect}
      nodeTypes={nodeTypes}
      fitView
      style={rfStyle}
    />
  );
}
```

----------------------------------------

TITLE: Initializing React Flow Application with Custom Node Types (React)
DESCRIPTION: This snippet defines the main `App` component for a React Flow application. It sets up custom `nodeTypes` for an 'osc' (oscillator) node and renders the `ReactFlow` component, connecting it to a Zustand store for managing nodes and edges. It also includes a `Background` component.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_14

LANGUAGE: JSX
CODE:
```
});

const nodeTypes = {
  osc: Osc,
};

export default function App() {
  const store = useStore(selector, shallow);

  return (
    <ReactFlow
      nodes={store.nodes}
      nodeTypes={nodeTypes}
      edges={store.edges}
      onNodesChange={store.onNodesChange}
      onEdgesChange={store.onEdgesChange}
      onConnect={store.addEdge}
    >
      <Background />
    </ReactFlow>
  );
}
```

----------------------------------------

TITLE: Defining the Connection Type in TypeScript
DESCRIPTION: This TypeScript snippet defines the `Connection` type, a fundamental data structure in `xyflow` for representing a basic link between two nodes. It specifies four properties: `source` and `target` (node IDs), and `sourceHandle` and `targetHandle` (optional handle IDs), which are crucial for establishing connections in a flow. This type is often used as an input to the `addEdge` utility to create a more comprehensive `Edge` object.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/connection.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type Connection = {
  source: string;
  target: string;
  sourceHandle: string | null;
  targetHandle: string | null;
};
```

----------------------------------------

TITLE: Wrapping SvelteFlow with SvelteFlowProvider (App.svelte)
DESCRIPTION: Demonstrates how to wrap the main SvelteFlow component and other child components (like a Sidebar) with SvelteFlowProvider to make the flow context available to them.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/svelte-flow-provider.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script>
  import { SvelteFlow, SvelteFlowProvider } from '@xyflow/svelte';
  import '@xyflow/svelte/dist/style.css';

  import Sidebar from './Sidebar.svelte';

  /* ... */
</script>

<SvelteFlowProvider>
  <SvelteFlow bind:nodes bind:edges />
  <Sidebar />
</SvelteFlowProvider>
```

----------------------------------------

TITLE: Defining Union Node Types in TypeScript for ReactFlow (v12)
DESCRIPTION: This snippet shows the new approach in ReactFlow v12 for defining multiple node types with distinct data structures using TypeScript union types. This allows for better type safety and distinction based on the `node.type` attribute.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_17

LANGUAGE: typescript
CODE:
```
type NumberNode = Node<{ value: number }, 'number'>;
type TextNode = Node<{ text: string }, 'text'>;
type AppNode = NumberNode | TextNode;
```

----------------------------------------

TITLE: Defining a Custom Node Type in Svelte Flow
DESCRIPTION: Shows how to define a custom node type (`NumberNodeType`) by extending the base `Node` type with specific data properties and a custom type identifier. Demonstrates using this custom type within a Svelte component's `<script lang="ts">` block via `NodeProps` to access typed data.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/advanced/typescript.mdx#_snippet_1

LANGUAGE: svelte
CODE:
```
<script module>
  export type NumberNodeType = Node<{ number: number }, 'number'>;
</script>

<script lang="ts">
  import { Handle, Position, type NodeProps, type Node } from '@xyflow/svelte';

  let { id, data }: NodeProps<NumberNodeType> = $props();
</script>

<div class="custom">
  <div>A special number: {data.number}</div>
  <Handle type="source" position={Position.Right} />
</div>
```

----------------------------------------

TITLE: Using ControlButton with Controls (Svelte)
DESCRIPTION: This snippet demonstrates how to import and use the `ControlButton` component within the `Controls` component in a Svelte application. It shows a basic example of adding a button with an `onclick` handler to perform an action when clicked.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/control-button.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { Controls, ControlButton } from '@xyflow/svelte';
</script>

<Controls>
  <ControlButton onclick={() => console.log('⚡️')}>
    ⚡️
  </ControlButton>
</Controls>
```

----------------------------------------

TITLE: Implementing Custom Svelte Edge with EdgeLabel
DESCRIPTION: This Svelte code snippet demonstrates how to create a custom edge component using `@xyflow/svelte`. It utilizes `BaseEdge` for rendering the edge path and conditionally includes `EdgeLabel` to display a clickable label. It calculates the Bezier path and label position based on edge props.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/edge-label.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { BaseEdge, getBezierPath, type EdgeProps } from '@xyflow/svelte';

  let {
    label,
    labelStyle,
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition
  } : EdgeProps = $props();

  let [path, labelX, labelY] = $derived(getBezierPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
    curvature: pathOptions?.curvature
  }));
</script>

<BaseEdge
  {path}
>

{#if label}
  <EdgeLabel x={labelX} y={labelY} style={labelStyle}>
    {label}
  </EdgeLabel>
{/if}
```

----------------------------------------

TITLE: Creating a Custom Node with Tailwind (Svelte)
DESCRIPTION: This Svelte component shows how to build a custom node for Svelte Flow and style it using Tailwind CSS utility classes. It includes Handles for connecting edges and uses NodeProps to access node data.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/customization/theming.mdx#_snippet_3

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { Handle, Position, type NodeProps } from '@xyflow/svelte';

  let { data }: NodeProps = $props();
</script>

<div class="px-4 py-2 shadow-md rounded-md bg-white border-2 border-stone-400">
  <div class="flex">
    <div class="rounded-full w-12 h-12 flex justify-center items-center bg-gray-100">
      {data.emoji}
    </div>
    <div class="ml-2">
      <div class="text-lg font-bold">{data.name}</div>
      <div class="text-gray-500">{data.job}</div>
    </div>
  </div>
  <Handle
    type="target"
    position={Position.Top}
    class="w-16 !bg-teal-500 rounded-none border-none"
  />
  <Handle
    type="source"
    position={Position.Bottom}
    class="w-16 !bg-teal-500 rounded-none border-none"
  />
</div>
```

----------------------------------------

TITLE: Using Built-In Components with Svelte Flow
DESCRIPTION: This snippet demonstrates how to integrate the standard built-in components (MiniMap, Controls, Background, and Panel) into a Svelte Flow instance. It requires the '@xyflow/svelte' package and its styles. The components are added as direct children of the <SvelteFlow> component.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/getting-started/built-in-components.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script>
  import { SvelteFlow, MiniMap, Controls, Background, Panel } from '@xyflow/svelte';

  import '@xyflow/svelte/dist/style.css';
</script>

<SvelteFlow>
  <MiniMap />
  <Controls />
  <Background />
  <Panel position="top-left">
    <h1>My Flow</h1>
  </Panel>
</SvelteFlow>
```

----------------------------------------

TITLE: Defining NodeHandle Type in TypeScript
DESCRIPTION: This snippet defines the `NodeHandle` type, an essential structure for representing connection points on nodes in the xyflow system. It specifies properties like `x` and `y` coordinates, `position`, an optional `id`, `width`, `height`, and `type` (source or target) to configure how handles behave and are rendered.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/node-handle.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type NodeHandle = {
  x: number,
  y: number,
  position: Position,
  id?: string | null,
  width?: number,
  height?: number,
  type?: 'source' | 'target',
}
```

----------------------------------------

TITLE: Basic Usage of useNodes() in React
DESCRIPTION: Demonstrates the basic usage of the `useNodes` hook from `@xyflow/react` to retrieve an array of all current nodes. The component re-renders whenever any node in the flow changes, displaying the total count of nodes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-nodes.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useNodes } from '@xyflow/react';

export default function () {
  const nodes = useNodes();

  return <div>There are currently {nodes.length} nodes!</div>;
}
```

----------------------------------------

TITLE: Using useEdges Hook in a React Component
DESCRIPTION: This snippet demonstrates how to import and use the `useEdges` hook within a React functional component. It retrieves the current array of edges from the React Flow instance and displays their count, causing the component to re-render whenever any edge changes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-edges.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useEdges } from '@xyflow/react';

export default function () {
  const edges = useEdges();

  return <div>There are currently {edges.length} edges!</div>;
}
```

----------------------------------------

TITLE: Implementing Interactive Oscillator Node with Zustand (React/JSX)
DESCRIPTION: This updated `Osc` component integrates the `updateNode` action from the Zustand store to enable interactive controls. It uses a selector to create `setFrequency` and `setType` event handlers, which update the node's data in the store when input values change. The `nodrag` class is used to prevent accidental node dragging.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_12

LANGUAGE: jsx
CODE:
```
import React from 'react';
import { Handle } from '@xyflow/react';
import { shallow } from 'zustand/shallow';

import { useStore } from '../store';

const selector = (id) => (store) => ({
  setFrequency: (e) => store.updateNode(id, { frequency: +e.target.value }),
  setType: (e) => store.updateNode(id, { type: e.target.value }),
});

export default function Osc({ id, data }) {
  const { setFrequency, setType } = useStore(selector(id), shallow);

  return (
    <div>
      <div>
        <p>Oscillator Node</p>

        <label>
          <span>Frequency:</span>
          <input
            className="nodrag"
            type="range"
            min="10"
            max="1000"
            value={data.frequency}
            onChange={setFrequency}
          />
          <span>{data.frequency}Hz</span>
        </label>

        <label>
          <span>Waveform:</span>
          <select className="nodrag" value={data.type} onChange={setType}>
            <option value="sine">sine</option>
            <option value="triangle">triangle</option>
            <option value="sawtooth">sawtooth</option>
            <option value="square">square</option>
          </select>
        </label>
      </div>

      <Handle type="source" position="bottom" />
    </div>
  );
}
```

----------------------------------------

TITLE: Registering Custom Node and Initializing React Flow Canvas
DESCRIPTION: This `App.tsx` file imports the `ReactFlow` component and the custom `Slide` node. It defines `nodeTypes` to register the `Slide` component as a 'slide' type node and initializes the `ReactFlow` canvas with a single 'slide' node at position (0,0), demonstrating how to display custom nodes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
import { Slide } from './Slide.tsx';

const nodeTypes = {
  slide: Slide,
};

export default function App() {
  const nodes = [{ id: '0', type: 'slide', position: { x: 0, y: 0 }, data: {} }];

  return <ReactFlow nodes={nodes} nodeTypes={nodeTypes} fitView />;
}
```

----------------------------------------

TITLE: Custom Handle with Connection Validation (JSX)
DESCRIPTION: This example illustrates how to create a custom handle that includes connection validation logic. It uses the `isValidConnection` prop to ensure that a connection is only valid if the source of the incoming connection matches a specified `source` prop. It also demonstrates the `onConnect` callback and inline styling.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/handle.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
import { Handle, Position } from '@xyflow/react';

export const TargetHandleWithValidation = ({ position, source }) => (
  <Handle
    type="target"
    position={position}
    isValidConnection={(connection) => connection.source === source}
    onConnect={(params) => console.log('handle onConnect', params)}
    style={{ background: '#fff' }}
  />
);
```

----------------------------------------

TITLE: Accessing New `positionAbsoluteX` and `positionAbsoluteY` in Custom Nodes (v12)
DESCRIPTION: This snippet demonstrates how to access the new `positionAbsoluteX` and `positionAbsoluteY` props in custom ReactFlow nodes in v12. These replace the old `xPos` and `yPos` for clearer absolute positioning.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_14

LANGUAGE: javascript
CODE:
```
function CustomNode({ positionAbsoluteX, positionAbsoluteY }) {
  ...
}
```

----------------------------------------

TITLE: Implementing a Custom Edge Type in React Flow
DESCRIPTION: This snippet shows how to define and render a custom edge type (`CustomEdge`) in React Flow. It extends `BaseEdge` and uses `getStraightPath` to calculate the edge path, demonstrating how to pass a custom `Edge` type as a generic to `EdgeProps` for type-safe access to custom edge data.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/typescript.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
import { getStraightPath, BaseEdge, type EdgeProps, type Edge } from '@xyflow/react';

type CustomEdge = Edge<{ value: number }, 'custom'>;

export default function CustomEdge({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
}: EdgeProps<CustomEdge>) {
  const [edgePath] = getStraightPath({ sourceX, sourceY, targetX, targetY });

  return <BaseEdge id={id} path={edgePath} />;
}
```

----------------------------------------

TITLE: Setting Default Edge Options in React Flow
DESCRIPTION: This snippet demonstrates how to apply default options to all newly created edges using the `defaultEdgeOptions` prop on the `ReactFlow` component. This is a convenient way to ensure consistent styling or behavior, such as making all edges animated, without modifying the `onConnect` handler.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/concepts/core-concepts.mdx#_snippet_2

LANGUAGE: JSX
CODE:
```
const defaultEdgeOptions = { animated: true };
...
<ReactFlow
  nodes={nodes}
  edges={edges}
  onNodesChange={onNodesChange}
  onEdgesChange={onEdgesChange}
  onConnect={onConnect}
  defaultEdgeOptions={defaultEdgeOptions}
/>;
```

----------------------------------------

TITLE: Displaying Custom Edge Labels with EdgeText in React
DESCRIPTION: This React functional component, `CustomEdgeLabel`, demonstrates the usage of the `EdgeText` component from `@xyflow/react`. It positions a text label at specific coordinates (x=100, y=100) on an edge, applying custom styles for the label text (white fill) and its background (red fill, padding, border radius). The `label` prop dynamically sets the text content.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/edge-text.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { EdgeText } from '@xyflow/react';

export function CustomEdgeLabel({ label }) {
  return (
    <EdgeText
      x={100}
      y={100}
      label={label}
      labelStyle={{ fill: 'white' }}
      labelShowBg
      labelBgStyle={{ fill: 'red' }}
      labelBgPadding={[2, 4]}
      labelBgBorderRadius={2}
    />
  );
}
```

----------------------------------------

TITLE: Defining Edges with Multiple Source Handles in React Flow (JS)
DESCRIPTION: This JavaScript snippet illustrates how to define `initialEdges` for a React Flow graph when a source node has multiple handles. By specifying `sourceHandle` with a unique ID (e.g., 'a' or 'b'), you can precisely connect edges to specific connection points on the node, ensuring accurate graph layout and interaction. This is crucial when a node offers multiple distinct connection interfaces.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/custom-nodes.mdx#_snippet_6

LANGUAGE: js
CODE:
```
const initialEdges = [
  { id: 'edge-1', source: 'node-1', sourceHandle: 'a', target: 'node-2' },
  { id: 'edge-2', source: 'node-1', sourceHandle: 'b', target: 'node-3' },
];
```

----------------------------------------

TITLE: Create Custom Centered Mind Map Edge in React Flow TSX
DESCRIPTION: Defines a custom React Flow edge component (`MindMapEdge`) that extends `BaseEdge`. It calculates the edge path using `getStraightPath` but modifies the `sourceY` coordinate to ensure the edge originates from the vertical center of the source node rather than the default handle position.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_26

LANGUAGE: tsx
CODE:
```
import { BaseEdge, EdgeProps, getStraightPath } from '@xyflow/react';

function MindMapEdge(props: EdgeProps) {
  const { sourceX, sourceY, targetX, targetY } = props;

  const [edgePath] = getStraightPath({
    sourceX,
    sourceY: sourceY + 20,
    targetX,
    targetY,
  });

  return <BaseEdge path={edgePath} {...props} />;
}

export default MindMapEdge;
```

----------------------------------------

TITLE: Implementing Controlled React Flow with New API (JSX)
DESCRIPTION: This snippet demonstrates how to set up a controlled React Flow component using the new API. It utilizes `useState` and `useCallback` hooks to manage nodes and edges, applying changes with `applyNodeChanges`, `applyEdgeChanges`, and `addEdge` functions for full control over the flow's state.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
import { useState, useCallback } from 'react';
import {
  ReactFlow,
  applyNodeChanges,
  applyEdgeChanges,
  addEdge,
} from 'react-flow-renderer';

const initialNodes = [
  { id: '1', data: { label: 'Node 1' }, position: { x: 250, y: 0 } },
  { id: '2', data: { label: 'Node 2' }, position: { x: 150, y: 100 } },
];

const initialEdges = [{ id: 'e1-2', source: '1', target: '2' }];

const BasicFlow = () => {
  const [nodes, setNodes] = useState(initialNodes);
  const [edges, setEdges] = useState(initialEdges);

  const onNodesChange = useCallback(
    (changes) => setNodes((ns) => applyNodeChanges(changes, ns)),
    [],
  );
  const onEdgesChange = useCallback(
    (changes) => setEdges((es) => applyEdgeChanges(changes, es)),
    [],
  );
  const onConnect = useCallback((connection) =>
    setEdges((eds) => addEdge(connection, eds)),
  );

  return (
    <ReactFlow
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onConnect={onConnect}
    />
  );
};

export default BasicFlow;
```

----------------------------------------

TITLE: Accessing React Flow State Without Provider - JSX
DESCRIPTION: This snippet demonstrates an incorrect way to access the React Flow state using `useReactFlow` outside of a `ReactFlowProvider` context. It will result in an error because the component is not wrapped by the necessary provider, preventing access to the internal state.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

function FlowWithoutProvider(props) {
  // cannot access the state here
  const reactFlowInstance = useReactFlow();

  return <ReactFlow {...props} />;
}

export default FlowWithoutProvider;
```

----------------------------------------

TITLE: Integrating Controls Component with ReactFlow (TSX)
DESCRIPTION: This snippet demonstrates how to import and render the <Controls /> component within a ReactFlow instance. The component provides essential UI buttons for zooming, fitting the view, and locking the viewport, enhancing user interaction with the flow. It requires ReactFlow and Controls from @xyflow/react.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/controls.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
import { ReactFlow, Controls } from '@xyflow/react'

export default function Flow() {
  return (
    <ReactFlow nodes={[...]} edges={[...]}>
      <Controls />
    </ReactFlow>
  )
}
```

----------------------------------------

TITLE: Defining NodeOrigin Type in TypeScript
DESCRIPTION: This TypeScript type definition specifies NodeOrigin as a tuple of two numbers, representing the x and y coordinates for a node's origin. The values typically range from 0 to 1, where [0,0] is top-left and [1,1] is bottom-right, influencing how a node is positioned relative to its own coordinates.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/node-origin.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type NodeOrigin = [number, number];
```

----------------------------------------

TITLE: Defining NodeChange Union Type in TypeScript
DESCRIPTION: This TypeScript snippet defines the `NodeChange` type as a union of six distinct node change types. This union type is crucial for the `onNodesChange` callback, allowing it to handle various modifications to nodes within an xyflow application, such as dimension, position, selection, addition, removal, or replacement.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/node-change.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type NodeChange =
  | NodeDimensionChange
  | NodePositionChange
  | NodeSelectionChange
  | NodeRemoveChange
  | NodeAddChange
  | NodeReplaceChange;
```

----------------------------------------

TITLE: Adding Custom Control Button to ReactFlow Controls (JSX)
DESCRIPTION: This snippet demonstrates how to integrate a custom button, ControlButton, into the Controls component of ReactFlow. It shows importing necessary components and defining an onClick handler for the button, which triggers an alert when clicked. This allows for extending the default control panel with custom functionality.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/control-button.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { MagicWand } from '@radix-ui/react-icons'
import { ReactFlow, Controls, ControlButton } from '@xyflow/react'

export default function Flow() {
  return (
    <ReactFlow nodes={[...]} edges={[...]}>
      <Controls>
        <ControlButton onClick={() => alert('Something magical just happened. ✨')}>
          <MagicWand />
        </ControlButton>
      </Controls>
    </ReactFlow>
  )
}
```

----------------------------------------

TITLE: Accessing Store Value with useStore Hook (Svelte)
DESCRIPTION: This snippet demonstrates how to import and use the useStore hook in a Svelte component to access a value from the internal Svelte Flow store, such as the current connectionMode.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/hooks/use-store.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { useStore } from '@xyflow/svelte';

  // lots of props that you pass to the <SvelteFlow> component end up in the internal store.
  // Here we are accessing the current connectionMode.
  const { connectionMode } = useStore();
</script>
```

----------------------------------------

TITLE: Generating Static HTML from React Flow Component - JavaScript
DESCRIPTION: This JavaScript function `toHTML` demonstrates how to render a React Flow component into a static HTML string using `renderToStaticMarkup` from `react-dom/server`. It takes `nodes`, `edges`, `width`, and `height` as parameters to configure the React Flow instance, including a `Background` component. The output is a raw HTML string suitable for server-side rendering or static file creation.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/ssr-ssg-configuration.mdx#_snippet_4

LANGUAGE: javascript
CODE:
```
import React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import { ReactFlow, Background } from '@xyflow/react';

function toHTML({ nodes, edges, width, height }) {
  const html = renderToStaticMarkup(
    React.createElement(
      ReactFlow,
      {
        nodes,
        edges,
        width,
        height,
        minZoom: 0.2,
        fitView: true,
      },
      React.createElement(Background, null),
    ),
  );

  return html;
}
```

----------------------------------------

TITLE: Positioning an Edge Label with Path Utilities in React Flow (JSX)
DESCRIPTION: This snippet demonstrates how to accurately position an edge label (e.g., a button) at the midpoint of an edge. It leverages the `labelX` and `labelY` coordinates returned by `getStraightPath` and applies CSS `transform` for precise centering and translation. It also highlights the importance of `pointer-events: all` for interactivity and `nodrag`/`nopan` classes to prevent unwanted canvas interactions.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/custom-edges.mdx#_snippet_3

LANGUAGE: jsx
CODE:
```
export default function CustomEdge({ id, sourceX, sourceY, targetX, targetY }) {
  const { setEdges } = useReactFlow();
  const [edgePath, labelX, labelY] = getStraightPath({ ... });

  return (
    ...
        <button
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${labelX}px, ${labelY}px)`,
            pointerEvents: 'all',
          }}
          className="nodrag nopan"
          onClick={() => {
            setEdges((es) => es.filter((e) => e.id !== id));
          }}
        >
    ...
  );
}
```

----------------------------------------

TITLE: Configuring React Flow in main.jsx
DESCRIPTION: This snippet configures the main entry point of the React application (`main.jsx`) to integrate React Flow. It imports the necessary `ReactFlowProvider` and React Flow's CSS styles, wraps the `App` component within a `div` with defined dimensions, and encloses it in `ReactFlowProvider` to enable React Flow hooks.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
import App from './App';
import React from 'react';
import ReactDOM from 'react-dom/client';
import { ReactFlowProvider } from '@xyflow/react';

// 👇 Don't forget to import the styles!
import '@xyflow/react/dist/style.css';
import './index.css';

const root = document.querySelector('#root');

ReactDOM.createRoot(root).render(
  <React.StrictMode>
    {/* React flow needs to be inside an element with a known height and width to work */}
    <div style={{ width: '100vw', height: '100vh' }}>
      <ReactFlowProvider>
        <App />
      </ReactFlowProvider>
    </div>
  </React.StrictMode>
);
```

----------------------------------------

TITLE: Initializing React Flow Instance with Zoom (New API) - JSX
DESCRIPTION: This snippet demonstrates how to use the `onInit` handler to access the `ReactFlowInstance` and programmatically zoom the canvas. The `zoomTo` method is called with a zoom level of 2 immediately after initialization, providing initial view control.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_11

LANGUAGE: jsx
CODE:
```
const onInit = (reactFlowInstance: ReactFlowInstance) => reactFlowInstance.zoomTo(2);
...
<ReactFlow
   ...
  onInit={onInit}
/>
```

----------------------------------------

TITLE: Preventing Drag/Selection on Node Elements (Svelte)
DESCRIPTION: Demonstrates adding the 'nodrag' class to an element within a custom Svelte node. Elements with this class will not trigger the default node drag or selection behavior when interacted with.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/node-props.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<div>
  <input className="nodrag" type="range" min={0} max={100} />
</div>
```

----------------------------------------

TITLE: Using Store Action in Connect End Handler (React/TypeScript)
DESCRIPTION: Refines the `onConnectEnd` handler to integrate with the application's Zustand store for state management. It retrieves the store instance via `useStoreApi`, accesses the `nodeLookup` to find the parent node object, calculates the desired position for the new child node using a helper function, and calls the `addChildNode` store action to add the new node and edge if the connection ends on the pane and a valid parent is found.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_14

LANGUAGE: tsx
CODE:
```
const store = useStoreApi();

const onConnectEnd: OnConnectEnd = useCallback(
  (event) => {
    const { nodeLookup } = store.getState();
    const targetIsPane = (event.target as Element).classList.contains('react-flow__pane');

    if (targetIsPane && connectingNodeId.current) {
      const parentNode = nodeLookup.get(connectingNodeId.current);
      const childNodePosition = getChildNodePosition(event, parentNode);

      if (parentNode && childNodePosition) {
        addChildNode(parentNode, childNodePosition);
      }
    }
  },
  [getChildNodePosition],
);
```

----------------------------------------

TITLE: Rendering Custom Components within Viewport using ViewportPortal (JSX)
DESCRIPTION: This snippet demonstrates how to use the ViewportPortal component from @xyflow/react to render a custom div element directly within the React Flow viewport. The div is positioned using CSS transform to align with the flow's coordinate system, ensuring it is affected by zooming and panning. This is useful for creating custom overlays or interactive elements that need to be part of the flow's visual space.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/viewport-portal.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import React from 'react';
import { ViewportPortal } from '@xyflow/react';

export default function () {
  return (
    <ViewportPortal>
      <div
        style={{ transform: 'translate(100px, 100px)', position: 'absolute' }}
      >
        This div is positioned at [100, 100] on the flow.
      </div>
    </ViewportPortal>
  );
}
```

----------------------------------------

TITLE: Dynamically Updating Node Handles with useUpdateNodeInternals in React
DESCRIPTION: This React component demonstrates how to dynamically add or remove handles from a node using `useState` and `useCallback`, and then inform React Flow about these changes using `useUpdateNodeInternals`. It allows users to randomize the number of handles on a node, ensuring React Flow correctly re-renders and positions them. Dependencies include `react` for state and callbacks, and `@xyflow/react` for `Handle` and `useUpdateNodeInternals`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-update-node-internals.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useCallback, useState } from 'react';
import { Handle, useUpdateNodeInternals } from '@xyflow/react';

export default function RandomHandleNode({ id }) {
  const updateNodeInternals = useUpdateNodeInternals();
  const [handleCount, setHandleCount] = useState(0);
  const randomizeHandleCount = useCallback(() => {
    setHandleCount(Math.floor(Math.random() * 10));
    updateNodeInternals(id);
  }, [id, updateNodeInternals]);

  return (
    <>
      {Array.from({ length: handleCount }).map((_, index) => (
        <Handle
          key={index}
          type="target"
          position="left"
          id={`handle-${index}`}
        />
      ))}

      <div>
        <button onClick={randomizeHandleCount}>Randomize handle count</button>
        <p>There are {handleCount} handles on this node.</p>
      </div>
    </>
  );
}
```

----------------------------------------

TITLE: Integrating MiniMap with ReactFlow
DESCRIPTION: This snippet demonstrates the basic integration of the <MiniMap /> component within a ReactFlow instance. It renders an overview of the flow, with nodeStrokeWidth set to 3 for visual emphasis on node outlines. This provides a static visual representation of the flow structure.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/minimap.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { ReactFlow, MiniMap } from '@xyflow/react';

export default function Flow() {
  return (
    <ReactFlow nodes={[...]]} edges={[...]]}>
      <MiniMap nodeStrokeWidth={3} />
    </ReactFlow>
  );
}
```

----------------------------------------

TITLE: Configuring ReactFlowProvider for Server-Side Rendering with Initial Data (JSX)
DESCRIPTION: This example shows how to configure the `ReactFlowProvider` for server-side rendering by passing `initialNodes`, `initialEdges`, `initialWidth`, `initialHeight`, and `fitView` props. These `initial-` prefixed values are used only for the first render, allowing the provider to set up the flow's state correctly in an SSR environment before client-side hydration.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/ssr-ssg-configuration.mdx#_snippet_3

LANGUAGE: JSX
CODE:
```
<ReactFlowProvider
  initialNodes={nodes}
  initialEdges={edges}
  initialWidth={1000}
  initialHeight={500}
  fitView
>
  <App />
</ReactFlowProvider>
```

----------------------------------------

TITLE: Importing Default React Flow Styles (JavaScript)
DESCRIPTION: This snippet imports the default CSS styles for React Flow. It's typically placed in the main application entry point, such as `App.jsx`, to apply basic styling for built-in nodes and edges.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/theming.mdx#_snippet_0

LANGUAGE: js
CODE:
```
import '@xyflow/react/dist/style.css';
```

----------------------------------------

TITLE: Defining ConnectionState Type in TypeScript
DESCRIPTION: This TypeScript type definition describes the `ConnectionState` which bundles information about an ongoing connection. It includes two union types: `NoConnection` for when no connection is in progress, and `ConnectionInProgress` for an active connection, detailing source and target handle/node information. This type is typically used with the `useConnection` hook.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/connection-state.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
type NoConnection = {
  inProgress: false;
  isValid: null;
  from: null;
  fromHandle: null;
  fromPosition: null;
  fromNode: null;
  to: null;
  toHandle: null;
  toPosition: null;
  toNode: null;
};
type ConnectionInProgress = {
  inProgress: true;
  isValid: boolean | null;
  from: XYPosition;
  fromHandle: Handle;
  fromPosition: Position;
  fromNode: NodeBase;
  to: XYPosition;
  toHandle: Handle | null;
  toPosition: Position;
  toNode: NodeBase | null;
};

type ConnectionState = ConnectionInProgress | NoConnection;
```

----------------------------------------

TITLE: Correctly Defining an Edge with Source and Target in React Flow
DESCRIPTION: This snippet illustrates the correct way to define an edge in React Flow. By providing valid `source` and `target` IDs, the edge can be properly rendered, connecting the specified nodes in the flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_14

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';

const nodes = [
  /* ... */
];

const edges = [
  {
    source: '1',
    target: '2',
  },
];

function Flow(props) {
  return <ReactFlow nodes={nodes} edges={edges} />;
}
```

----------------------------------------

TITLE: Listening for Selection Changes in SvelteFlow (Svelte)
DESCRIPTION: This snippet demonstrates how to use the `useOnSelectionChange` hook in a Svelte component to track selected nodes and edges. It updates local state variables `selectedNodes` and `selectedEdges` with the IDs of the currently selected elements and displays them.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/hooks/use-on-selection-change.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script>
  import { useOnSelectionChange } from '@xyflow/svelte';

  let selectedNodes = $state.raw([]);
  let selectedEdges = $state.raw([]);

  useOnSelectionChange(({ nodes, edges }) => {
    selectedNodes = nodes.map((node) => node.id);
    selectedEdges = edges.map((edge) => edge.id);
  });
</script>

<div>
    <p>Selected nodes: {selectedNodes.join(', ')}</p>
    <p>Selected edges: {selectedEdges.join(', ')}</p>
</div>
```

----------------------------------------

TITLE: Implementing Click-to-Focus Node Navigation in React Flow (TSX)
DESCRIPTION: This snippet adds click-to-focus functionality to the React Flow presentation. It utilizes the `useReactFlow` hook to access the `fitView` method and defines an `onNodeClick` handler. When a node is clicked, `fitView` is called to pan and zoom the canvas to center on the clicked node with a smooth animation.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_10

LANGUAGE: tsx
CODE:
```
import { useCallback } from 'react';
import { ReactFlow, useReactFlow, type NodeMouseHandler } from '@xyflow/react';
import { Slide, SlideData, SLIDE_WIDTH } from './Slide';

const slides: Record<string, SlideData> = {
  ...
}

const nodeTypes = {
  slide: Slide
};

const initialSlide = '0';
const { nodes, edges } = slidesToElements(initialSlide, slides);

export default function App() {
  const { fitView } = useReactFlow();
  const handleNodeClick = useCallback<NodeMouseHandler>(
    (_, node) => {
      fitView({ nodes: [node], duration: 150 });
    },
    [fitView]
  );

  return (
    <ReactFlow
      ...
      fitViewOptions={{ nodes: [{ id: initialSlide }] }}
      onNodeClick={handleNodeClick}
    />
  );
}
```

----------------------------------------

TITLE: Basic Custom Node with Handles (Svelte)
DESCRIPTION: Demonstrates how to use the <Handle /> component within a custom Svelte node to define connection points for target (left) and source (right) edges. It imports Handle, Position, and NodeProps from @xyflow/svelte.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/handle.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { Handle, Position, type NodeProps } from '@xyflow/svelte';

  let { data } : NodeProps = $props();
</script>

<div>
  {data.label}
</div>

<Handle type="target" position={Position.Left} />
<Handle type="source" position={Position.Right} />
```

----------------------------------------

TITLE: Defining Parent-Child Nodes in Svelte Flow (JavaScript)
DESCRIPTION: This snippet demonstrates how to define nodes with a parent-child relationship in Svelte Flow. It shows an array of nodes where node 'B' is designated as a child of node 'A' using the `parentId` property. Child nodes are positioned relative to their parent.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/layouting/sub-flows.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
let nodes = $state.raw([
  {
    id: 'A',
    data: { label: 'parent' },
    position: { x: 0, y: 0 },
  },
  {
    id: 'B',
    data: { label: 'child' },
    position: { x: 10, y: 10 },
    parentId: 'A',
  },
]);
```

----------------------------------------

TITLE: Overriding Default Node Background Color in CSS
DESCRIPTION: This CSS snippet demonstrates how to override the default background color for nodes within the xyflow library. By targeting the `.react-flow` class, you can set a new value for the `--xy-node-background-color-default` CSS variable, changing the appearance of all nodes that use this default. This allows for global theme adjustments without modifying individual element styles.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/theming.mdx#_snippet_4

LANGUAGE: CSS
CODE:
```
.react-flow {
  --xy-node-background-color-default: #ff5050;
}
```

----------------------------------------

TITLE: Accessing Flow State in Child Component (Sidebar.svelte)
DESCRIPTION: Shows how a component nested within SvelteFlowProvider can use hooks like useNodes() to access and display flow state (e.g., node positions). This hook relies on the context provided by SvelteFlowProvider.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/svelte-flow-provider.mdx#_snippet_1

LANGUAGE: svelte
CODE:
```
<script>
  import { SvelteFlow, SvelteFlowProvider } from '@xyflow/svelte'

  // This hook will only work if the component it's used in
  // is a child of <SvelteFlowProvider />
  const nodes = useNodes()
</script>

<aside>
  {#each nodes.current as node (node.id)}
    <div key={node.id}>
      Node {node.id} -
        x: {node.position.x.toFixed(2)},
        y: {node.position.y.toFixed(2)}
    </div>
  {/each}
</aside>
```

----------------------------------------

TITLE: Specifying Custom Node Types with useInternalNode in TypeScript
DESCRIPTION: This snippet illustrates how to use the `useInternalNode` hook with a generic type argument in TypeScript. By providing `CustomNodeType`, developers can ensure type safety and proper inference when working with custom node structures, aligning with advanced TypeScript usage in `@xyflow/react`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-internal-node.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
const internalNode = useInternalNode<CustomNodeType>();
```

----------------------------------------

TITLE: Using Controls Component in SvelteFlow
DESCRIPTION: This snippet demonstrates how to import and use the <Controls /> component within a <SvelteFlow /> instance. It initializes empty nodes and edges state and renders the flow with the default controls panel.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/controls.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { SvelteFlow, Controls } from '@xyflow/svelte';

  let nodes = $state.raw([]);
  let edges = $state.raw([]);
</script>

<SvelteFlow bind:nodes bind:edges>
  <Controls />
</SvelteFlow>
```

----------------------------------------

TITLE: Generating a Smooth Step Path with getSmoothStepPath (JavaScript)
DESCRIPTION: This snippet demonstrates how to use the `getSmoothStepPath` function from `@xyflow/svelte` to calculate the SVG path data for a smooth stepped edge between two points. It takes source and target coordinates and positions, returning the path string and coordinates for potential labels.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/utils/get-smooth-step-path.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { Position, getSmoothStepPath } from '@xyflow/svelte';

const source = { x: 0, y: 20 };
const target = { x: 150, y: 100 };

const [path, labelX, labelY, offsetX, offsetY] = getSmoothStepPath({
  sourceX: source.x,
  sourceY: source.y,
  sourcePosition: Position.Right,
  targetX: target.x,
  targetY: target.y,
  targetPosition: Position.Left,
});

console.log(path); //=> "M0 20L20 20L 70,20Q 75,20 75,25L 75,95Q ..."
console.log(labelX, labelY); //=> 75, 60
console.log(offsetX, offsetY); //=> 75, 40
```

----------------------------------------

TITLE: Using isNode() with a Node Object - JavaScript
DESCRIPTION: This snippet demonstrates how to import the `isNode` function from `@xyflow/svelte` and use it to check if a JavaScript object conforms to the structure of a Node. It shows a typical Node object definition and how `isNode` can be used in a conditional statement.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/utils/is-node.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { isNode } from '@xyflow/svelte';

const node = {
  id: 'node-a',
  data: {
    label: 'node',
  },
  position: {
    x: 0,
    y: 0,
  },
};

if (isNode(node)) {
  // ..
}
```

----------------------------------------

TITLE: Calculate Child Node Position in React Flow (TSX)
DESCRIPTION: Helper function to calculate the position for a new child node relative to its parent within a React Flow pane. It converts screen coordinates to flow coordinates and adjusts based on the parent node's absolute position and dimensions. Requires access to the store's DOM node and the parent node's computed properties.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_15

LANGUAGE: tsx
CODE:
```
const getChildNodePosition = (event: MouseEvent, parentNode?: Node) => {
  const { domNode } = store.getState();

  if (
    !domNode ||
    // we need to check if these properties exist, because when a node is not initialized yet,
    // it doesn't have a positionAbsolute nor a width or height
    !parentNode?.computed?.positionAbsolute ||
    !parentNode?.computed?.width ||
    !parentNode?.computed?.height
  ) {
    return;
  }

  const panePosition = screenToFlowPosition({
    x: event.clientX,
    y: event.clientY,
  });

  // we are calculating with positionAbsolute here because child nodes are positioned relative to their parent
  return {
    x:
      panePosition.x -
      parentNode.computed?.positionAbsolute.x +
      parentNode.computed?.width / 2,
    y:
      panePosition.y -
      parentNode.computed?.positionAbsolute.y +
      parentNode.computed?.height / 2,
  };
};
```

----------------------------------------

TITLE: Adding Custom Node Type to Svelte Flow (Svelte)
DESCRIPTION: This Svelte code snippet shows how to register a custom node component (`TextUpdaterNode`) with the `SvelteFlow` component. The custom node is added to the `nodeTypes` object, mapping a string key ('textUpdater') to the component, and this object is passed as a prop to `SvelteFlow`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/customization/custom-nodes.mdx#_snippet_1

LANGUAGE: svelte
CODE:
```
<script>
  import { SvelteFlow } from '@xyflow/svelte';
  import '@xyflow/svelte/dist/style.css';

  import TextUpdaterNode from './TextUpdaterNode.svelte';

  const nodeTypes = { textUpdater: TextUpdaterNode };

  // [...]

</script>

<SvelteFlow
  bind:nodes
  bind:edges
  {nodeTypes}
  fitView
>
  <!-- [...] -->
</SvelteFlow>
```

----------------------------------------

TITLE: Using useNodeConnections Hook with Target Handle
DESCRIPTION: This example demonstrates how to use the `useNodeConnections` hook to retrieve incoming connections for a specific target handle. It shows how to import the hook and then use it within a React component to display the number of connections, ensuring the component re-renders when connections change.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-node-connections.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useNodeConnections } from '@xyflow/react';

export default function () {
  const connections = useNodeConnections({
    handleType: 'target',
    handleId: 'my-handle',
  });

  return (
    <div>There are currently {connections.length} incoming connections!</div>
  );
}
```

----------------------------------------

TITLE: Exporting Svelte Flow as Image (Svelte)
DESCRIPTION: This snippet shows a Svelte component that renders a Svelte Flow diagram and includes a button to download the current view as a PNG image. It uses the `useSvelteFlow` hook to get the flow container element and the `html-to-image` library's `toPng` function to capture the image. Dependencies include `@xyflow/svelte` and `html-to-image`.
SOURCE: https://github.com/xyflow/web/blob/main/apps/example-apps/svelte/examples/misc/download-image/README.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { SvelteFlow, Controls, Background, useSvelteFlow } from '@xyflow/svelte';
  import { toPng } from 'html-to-image';

  const initialNodes = [
    { id: '1', position: { x: 0, y: 0 }, data: { label: 'Node 1' } },
    { id: '2', position: { x: 100, y: 100 }, data: { label: 'Node 2' } },
  ];

  const initialEdges = [{ id: 'e1-2', source: '1', target: '2' }];

  let flowContainer: HTMLElement;

  const { getNodes, getEdges, fitView } = useSvelteFlow();

  const downloadImage = () => {
    if (!flowContainer) {
      return;
    }

    toPng(flowContainer, {
      filter: (node) => {
        // we don't want to include the controls in the image
        if (node?.classList?.contains('svelte-flow__controls')) {
          return false;
        }
        return true;
      },
    })
      .then((dataUrl) => {
        const link = document.createElement('a');
        link.download = 'svelte-flow-example.png';
        link.href = dataUrl;
        link.click();
      })
      .catch((err) => {
        console.error('oops, something went wrong!', err);
      });
  };
</script>

<div class="download-image-flow" bind:this={flowContainer}>
  <SvelteFlow {initialNodes} {initialEdges} fitView>
    <Background />
    <Controls />
  </SvelteFlow>
</div>

<button on:click={downloadImage}>Download Image</button>

<style>
  .download-image-flow {
    width: 100%;
    height: 500px;
  }
</style>
```

----------------------------------------

TITLE: Importing React Flow Base Styles (JavaScript)
DESCRIPTION: This snippet demonstrates how to import the essential base CSS styles for React Flow. These styles are crucial for the library's correct functionality, even when opting for third-party styling solutions. It ensures that core components render as expected.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/theming.mdx#_snippet_5

LANGUAGE: js
CODE:
```
import '@xyflow/react/dist/base.css';
```

----------------------------------------

TITLE: Defining a Specific Custom Node Type in React Flow (Multiple)
DESCRIPTION: This example shows how to define a specific custom node type (`NumberNode`) when you have multiple custom nodes in your React Flow application. It uses a generic `NodeProps` type with the custom `Node` type to ensure type safety for the node's data structure. It also highlights the use of `type` instead of `interface` for node data definitions.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/typescript.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
import type { Node, NodeProps } from '@xyflow/react';

type NumberNode = Node<{ number: number }, 'number'>;

export default function NumberNode({ data }: NodeProps<NumberNode>) {
  return <div>A special number: {data.number}</div>;
}
```

----------------------------------------

TITLE: Cloning Svelte Flow Vite Template (bash)
DESCRIPTION: Command using npx and degit to quickly clone the official Svelte Flow Vite template repository into a new directory. Provides a pre-configured starting point.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/index.mdx#_snippet_1

LANGUAGE: bash
CODE:
```
npx degit xyflow/vite-svelte-flow-template app-name
```

----------------------------------------

TITLE: Defining the ResizeParams Type in TypeScript
DESCRIPTION: This snippet defines the `ResizeParams` type, which is a fundamental type used by the `<NodeResizer />` component in xyflow. It specifies the `x` and `y` coordinates, along with `width` and `height`, representing the dimensions of a resized node. This type is crucial for handling resize events and can be extended for additional context like resize direction.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/resize-params.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type ResizeParams = {
  x: number;
  y: number;
  width: number;
  height: number;
};
```

----------------------------------------

TITLE: Checking if an Object is an Edge in React Flow
DESCRIPTION: This snippet demonstrates how to use the `isEdge` function from `@xyflow/react` to check if a plain JavaScript object conforms to the `Edge` type. If `isEdge` returns `true`, TypeScript will narrow the type of the object, allowing for type-safe operations on it as an `Edge`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/is-edge.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { isEdge } from '@xyflow/react';

const edge = {
  id: 'edge-a',
  source: 'a',
  target: 'b'
};

if (isEdge(edge)) {
  // ...
}
```

----------------------------------------

TITLE: Implementing Custom Delete Action for Node Header (React Flow)
DESCRIPTION: This component defines a custom delete action button for a node header. When clicked, it removes the associated node from the React Flow graph. It utilizes `useNodeId` to get the current node's ID and `useReactFlow` to access the `setNodes` function for graph manipulation.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/components/nodes/node-header.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
export type NodeHeaderCustomDeleteActionProps = Omit<
  NodeHeaderActionProps,
  'onClick'
>;

/**
 * A custom delete action button that removes the node from the graph when clicked.
 */
export const NodeHeaderCustomDeleteAction = React.forwardRef<
  HTMLButtonElement,
  NodeHeaderCustomDeleteActionProps
>((props, ref) => {
  const id = useNodeId();
  const { setNodes } = useReactFlow();

  const handleClick = useCallback(() => {
    setNodes((prevNodes) => prevNodes.filter((node) => node.id !== id));
  }, []);

  return (
    <NodeHeaderAction
      ref={ref}
      onClick={handleClick}
      variant="ghost"
      {...props}
    >
      <Trash />
    </NodeHeaderAction>
  );
});

NodeHeaderCustomDeleteAction.displayName = 'NodeHeaderCustomDeleteAction';
```

----------------------------------------

TITLE: Implementing Color Input for Custom Node - JSX
DESCRIPTION: This JSX snippet shows the implementation of a color input element within a custom React Flow node. It sets the default value from the node's data.color and calls the updateNodeColor action from the Zustand store when the input value changes, updating the node's background color. The nodrag class prevents accidental dragging of the node when interacting with the input.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/state-management.mdx#_snippet_3

LANGUAGE: jsx
CODE:
```
<input
  type="color"
  defaultValue={data.color}
  onChange={(evt) => updateNodeColor(id, evt.target.value)}
  className="nodrag"
/>
```

----------------------------------------

TITLE: Overriding CSS Variables in Svelte Flow (CSS)
DESCRIPTION: This snippet demonstrates how to override the default CSS variables used by Svelte Flow. By targeting the `.svelte-flow` class, you can change the default appearance of elements like nodes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/customization/theming.mdx#_snippet_2

LANGUAGE: css
CODE:
```
.svelte-flow {
  --xy-node-background-color-default: #ff5050;
}
```

----------------------------------------

TITLE: Disabling Canvas Pan on Node Scroll (Svelte)
DESCRIPTION: Shows how to add the 'nowheel' class to a scrollable container inside a custom Svelte node. This prevents the main canvas from panning when the user scrolls within this specific element.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/node-props.mdx#_snippet_1

LANGUAGE: svelte
CODE:
```
<div className="nowheel" style={{ overflow: 'auto' }}>
  <p>Scrollable content...</p>
</div>
```

----------------------------------------

TITLE: Implementing a Type Guard for a Custom SvelteFlow Node (TypeScript)
DESCRIPTION: Provides an example of a TypeScript type guard function (`isNumberNode`) that checks if a given node object matches a specific custom node type (`NumberNodeType`). This function can be used with array methods like `filter` to narrow down the type of nodes in a list.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/advanced/typescript.mdx#_snippet_7

LANGUAGE: ts
CODE:
```
function isNumberNode(node: NodeType): node is NumberNodeType {
  return node.type === 'number';
}

// numberNodes is now correctly typed as NumberNodeType[]
let numberNodes = $derived(nodes.filter(isNumberNode));
```

----------------------------------------

TITLE: Defining SmoothStepEdge Type in React Flow (TypeScript)
DESCRIPTION: This snippet defines the specific properties for the `SmoothStepEdge` variant. It sets the `type` property to 'smoothstep' and introduces an optional `pathOptions` object, which can include `offset` and `borderRadius` for customizing the edge's path rendering.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/edge.mdx#_snippet_1

LANGUAGE: TypeScript
CODE:
```
type $ = {
  type: "smoothstep"
  pathOptions?: { offset?: number; borderRadius?: number }
}
export default $
```

----------------------------------------

TITLE: Defining BezierEdge Type in React Flow (TypeScript)
DESCRIPTION: This snippet defines the specific properties for the `BezierEdge` variant. It sets the `type` property to 'default' and includes an optional `pathOptions` object, which can specify `curvature` to control the bezier curve's shape.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/edge.mdx#_snippet_2

LANGUAGE: TypeScript
CODE:
```
type $ = {
  type: "default"
  pathOptions?: { curvature?: number }
}
export default $
```

----------------------------------------

TITLE: Binding Viewport - New API (Svelte)
DESCRIPTION: Shows the simplified Svelte 5 approach for binding the viewport state using a `$state` rune and Svelte's native `bind:` directive.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_13

LANGUAGE: svelte
CODE:
```
let viewport = $state < Viewport > { x: 100, y: 100, zoom: 1.25 };

<SvelteFlow bind:viewport />;
```

----------------------------------------

TITLE: Cloning Vite Template (Bash)
DESCRIPTION: Use the degit tool to quickly clone the official vite-react-flow-template repository into a new directory for a fast project setup.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/index.mdx#_snippet_0

LANGUAGE: Bash
CODE:
```
npx degit xyflow/vite-react-flow-template app-name
```

----------------------------------------

TITLE: Updating NodeProps Generic Type in Svelte Flow
DESCRIPTION: This snippet demonstrates the updated usage of the `NodeProps` generic in Svelte Flow 0.0.38. Previously, `NodeProps` represented only node data, but now it represents the entire node. This change requires updating type definitions in Svelte components to ensure compatibility with the new API.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/whats-new/2024-03-07.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
type $$Props = NodeProps<AppNode>
```

----------------------------------------

TITLE: Implementing Uncontrolled React Flow with defaultNodes/defaultEdges (JSX)
DESCRIPTION: This example demonstrates how to create a fully interactive React Flow instance using the `defaultNodes` and `defaultEdges` props. When these props are set, all node and edge actions (dragging, connecting, removing) are handled internally, requiring no additional handlers for basic functionality.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_3

LANGUAGE: jsx
CODE:
```
import ReactFlow from 'react-flow-renderer';

const defaultNodes = [
  { id: '1', data: { label: 'Node 1' }, position: { x: 250, y: 0 } },
  { id: '2', data: { label: 'Node 2' }, position: { x: 150, y: 100 } },
];

const defaultEdges = [{ id: 'e1-2', source: '1', target: '2' }];

const BasicFlow = () => {
  return <ReactFlow defaultNodes={defaultNodes} defaultEdges={defaultEdges} />;
};

export default BasicFlow;
```

----------------------------------------

TITLE: Importing Default Svelte Flow Styles (JavaScript)
DESCRIPTION: This snippet imports the complete set of default CSS styles provided by Svelte Flow. These styles offer sensible defaults for built-in nodes and edges, including padding, border radius, and animated edges, allowing users to quickly get started with a basic appearance.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/customization/theming.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import '@xyflow/svelte/dist/style.css';
```

----------------------------------------

TITLE: Implementing a Custom Counter Node in React
DESCRIPTION: This example demonstrates how to create a custom React node named `CounterNode` that utilizes `NodeProps` to access initial data. It shows state management with `useState` and renders a simple UI with an increment button, using the `nodrag` class to prevent dragging on the button.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/node-props.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
import { useState } from 'react';
import { NodeProps, Node } from '@xyflow/react';

export type CounterNode = Node<
  {
    initialCount?: number;
  },
  'counter'
>;

export default function CounterNode(props: NodeProps<CounterNode>) {
  const [count, setCount] = useState(props.data?.initialCount ?? 0);

  return (
    <div>
      <p>Count: {count}</p>
      <button className="nodrag" onClick={() => setCount(count + 1)}>
        Increment
      </button>
    </div>
  );
}
```

----------------------------------------

TITLE: Using useNodesInitialized for Node Layouting in React
DESCRIPTION: This snippet demonstrates how to use the `useNodesInitialized` hook to trigger a layout function once all nodes in a React Flow instance have been measured. It uses `useEffect` to re-layout nodes when `nodesInitialized` becomes true, ensuring accurate positioning based on node dimensions. The `options` object can be used to configure the hook's behavior, such as including hidden nodes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-nodes-initialized.mdx#_snippet_0

LANGUAGE: JSX
CODE:
```
import { useReactFlow, useNodesInitialized } from '@xyflow/react';
import { useEffect, useState } from 'react';

const options = {
  includeHiddenNodes: false,
};

export default function useLayout() {
  const { getNodes } = useReactFlow();
  const nodesInitialized = useNodesInitialized(options);
  const [layoutedNodes, setLayoutedNodes] = useState(getNodes());

  useEffect(() => {
    if (nodesInitialized) {
      setLayoutedNodes(yourLayoutingFunction(getNodes()));
    }
  }, [nodesInitialized]);

  return layoutedNodes;
}
```

----------------------------------------

TITLE: Creating an Empty React Flow in App.jsx
DESCRIPTION: This code defines the main `App` component, setting up an empty React Flow instance. It imports `ReactFlow` and `Background` components from `@xyflow/react`, rendering a basic flow with a background to verify the initial setup is correct.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_6

LANGUAGE: jsx
CODE:
```
import React from 'react';
import { ReactFlow, Background } from '@xyflow/react';

export default function App() {
  return (
    <ReactFlow>
      <Background />
    </ReactFlow>
  );
}
```

----------------------------------------

TITLE: Rendering Svelte Flow and Background (Svelte)
DESCRIPTION: Renders the SvelteFlow component within a container element with defined dimensions (100vw, 100vh) and places the Background component inside SvelteFlow for visual enhancement.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/getting-started/building-a-flow.mdx#_snippet_1

LANGUAGE: svelte
CODE:
```
<div style:width="100vw" style:height="100vh">
    <SvelteFlow>
        <Background />
    </SvelteFlow>
</div>
```

----------------------------------------

TITLE: Hiding Svelte Flow Attribution in Svelte
DESCRIPTION: This snippet demonstrates how to remove the default Svelte Flow attribution badge displayed in the corner of the flow. It requires importing the `SvelteFlow` component and its styles. The attribution is hidden by passing `hideAttribution: true` within the `proOptions` object to the `<SvelteFlow />` component.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/remove-attribution.mdx#_snippet_0

LANGUAGE: Svelte
CODE:
```
<script>
  import { SvelteFlow } from '@xyflow/svelte';
  import '@xyflow/svelte/dist/style.css';
</script>

<SvelteFlow
  proOptions={{ hideAttribution: true }}
/>
```

----------------------------------------

TITLE: Defining Custom Mind Map Edge Component (React/TypeScript)
DESCRIPTION: Defines a custom React Flow edge component named `MindMapEdge`. It accepts `EdgeProps` and utilizes the `getStraightPath` helper from `@xyflow/react` to calculate and render a straight line path between the source and target nodes. This component is designed specifically for use within a mind map visualization built with React Flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_10

LANGUAGE: tsx
CODE:
```
import { BaseEdge, EdgeProps, getStraightPath } from '@xyflow/react';

function MindMapEdge(props: EdgeProps) {
  const { sourceX, sourceY, targetX, targetY } = props;

  const [edgePath] = getStraightPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
  });

  return <BaseEdge path={edgePath} {...props} />;
}

export default MindMapEdge;
```

----------------------------------------

TITLE: Defining Svelte Flow Node Dimensions for SSR (Svelte)
DESCRIPTION: Shows how to explicitly set the `width` and `height` properties for a node object when configuring nodes for server-side rendering in Svelte Flow. This is necessary because browser layout calculations are not available on the server.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/advanced/server-side-rendering.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script>
  const nodes = [
    {
      id: '1',
      type: 'default',
      position: { x: 0, y: 0 },
      data: { label: 'Node 1' },
      width: 100,
      height: 50,
    },
  ];
</script>
```

----------------------------------------

TITLE: Using Background Component with SvelteFlow (Svelte)
DESCRIPTION: This snippet demonstrates how to integrate the <Background /> component into a <SvelteFlow /> instance. It shows how to bind nodes and edges, and how to configure the background with a specific color and the 'Dots' variant using BackgroundVariant.Dots.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/background.mdx#_snippet_0

LANGUAGE: Svelte
CODE:
```
<script lang="ts">
  import { SvelteFlow, Background, BackgroundVariant } from '@xyflow/svelte';

  let nodes = $state.raw([]);
  let edges = $state.raw([]);
</script>

<SvelteFlow bind:nodes bind:edges>
  <Background bgColor="#ccc" variant={BackgroundVariant.Dots} />
</SvelteFlow>
```

----------------------------------------

TITLE: Using Hooks (e.g., useEdges) - New API (Svelte)
DESCRIPTION: Shows the new pattern for Svelte Flow hooks in Svelte 5, where they return an object with a `.current` property containing the reactive value, accessed using `$inspect` or similar methods.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_10

LANGUAGE: svelte
CODE:
```
const edges = useEdges();
$inspect(edges.current);
```

----------------------------------------

TITLE: Detecting Key Presses with useKeyPress in React
DESCRIPTION: This example demonstrates how to use the `useKeyPress` hook to detect when the 'Space' key is pressed, or when a combination of 'Meta+s' (Cmd+s) or 'Strg+s' (Ctrl+s) is pressed. The hook returns a boolean indicating the pressed state, which can then be used to conditionally render UI elements. It's imported from `@xyflow/react` and can be used in any functional component.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-key-press.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useKeyPress } from '@xyflow/react';

export default function () {
  const spacePressed = useKeyPress('Space');
  const cmdAndSPressed = useKeyPress(['Meta+s', 'Strg+s']);

  return (
    <div>
      {spacePressed && <p>Space pressed!</p>}
      {cmdAndSPressed && <p>Cmd + S pressed!</p>}
    </div>
  );
}
```

----------------------------------------

TITLE: Using useUpdateNodeInternals Hook in Svelte
DESCRIPTION: This snippet demonstrates how to import and initialize the `useUpdateNodeInternals` hook within a Svelte component script. This hook is used to inform Svelte Flow about changes to a node's handles or internal dimensions, ensuring proper rendering and positioning.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/hooks/use-update-node-internals.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { Handle, useUpdateNodeInternals } from '@xyflow/svelte';

  const updateNodeInternals = useUpdateNodeInternals();
</script>
```

----------------------------------------

TITLE: Scaffolding a New React Project with Vite and TypeScript
DESCRIPTION: This command uses `npm create vite` to quickly set up a new React project. The `--template react-ts` flag specifies that the project should be initialized with React and TypeScript, providing a ready-to-use development environment.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_0

LANGUAGE: bash
CODE:
```
npm create vite@latest -- --template react-ts
```

----------------------------------------

TITLE: Defining an Edge Without Source and Target in React Flow (Incorrect)
DESCRIPTION: This example demonstrates an invalid edge definition in React Flow. An edge object must explicitly include `source` and `target` properties to connect nodes; omitting them prevents the edge from being rendered and triggers a warning.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_13

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

const nodes = [
  /* ... */
];

const edges = [
  {
    nosource: '1',
    notarget: '2',
  },
];

function Flow(props) {
  return <ReactFlow nodes={nodes} edges={edges} />;
}
```

----------------------------------------

TITLE: Handling Mismatched Custom Node Type Keys in React Flow (Incorrect)
DESCRIPTION: This example shows another common mistake where the `node.type` ('custom') does not exactly match the key in the `nodeTypes` object ('Custom'). React Flow requires an exact string match for custom node types to be correctly identified and rendered.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_7

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import MyCustomNode from './MyCustomNode';

const nodes = [
  {
    id: 'mycustomnode',
    type: 'custom',
    // ...
  },
];

const nodeTypes = {
  Custom: MyCustomNode,
};

function Flow(props) {
  // node.type and key in nodeTypes object are not exactly the same (capitalized)
  return <ReactFlow nodes={nodes} nodeTypes={nodeTypes} />;
}
```

----------------------------------------

TITLE: Setting React Flow Color Mode (JSX)
DESCRIPTION: This JSX snippet demonstrates how to set a built-in color mode for React Flow using the `colorMode` prop. Here, it's set to 'dark', which applies a corresponding class to the root element for theme-specific styling.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/theming.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
import ReactFlow from '@xyflow/react';

export default function Flow() {
  return <ReactFlow colorMode="dark" nodes={[...]} edges={[...]} />
}
```

----------------------------------------

TITLE: Getting Viewport Transform from Bounds (New) - JavaScript
DESCRIPTION: This snippet showcases the new `getViewportForBounds` utility. It returns the viewport transformation as an object `{ x, y, zoom }`, providing named properties for clarity and improved readability compared to the previous array-based return.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/whats-new/2023-11-10.mdx#_snippet_3

LANGUAGE: JavaScript
CODE:
```
const { x, y, zoom } = getViewportForBounds(bounds, width, height, 0.5, 2);
```

----------------------------------------

TITLE: Specifying Handle Positions for Server-Side Edge Rendering in React Flow (TypeScript)
DESCRIPTION: This example illustrates how to provide explicit handle position information using the `handles` property within a node definition. This is necessary for React Flow to render edges correctly on the server, as handle positions cannot be measured dynamically in an SSR context. Each handle requires `type`, `position`, `x`, and `y` coordinates.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/ssr-ssg-configuration.mdx#_snippet_1

LANGUAGE: TypeScript
CODE:
```
const nodes: Node[] = [
  {
    id: '1',
    type: 'default',
    position: { x: 0, y: 0 },
    data: { label: 'Node 1' },
    width: 100,
    height: 50,
    handles: [
      {
        type: 'target',
        position: Position.Top,
        x: 100 / 2,
        y: 0,
      },
      {
        type: 'source',
        position: Position.Bottom,
        x: 100 / 2,
        y: 50,
      },
    ],
  },
];
```

----------------------------------------

TITLE: Tracking Current Slide and Node Clicks in React Flow (TSX)
DESCRIPTION: This snippet initializes a React Flow component, setting up state to track the `currentSlide` and defining a `handleNodeClick` callback. When a node is clicked, it uses `fitView` to center the clicked node and updates the `currentSlide` state, which is crucial for subsequent keyboard navigation. It depends on `@xyflow/react` and a `Slide` component.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_14

LANGUAGE: tsx
CODE:
```
import { useState, useCallback } from 'react';
import { ReactFlow, useReactFlow } from '@xyflow/react';
import { Slide, SlideData, SLIDE_WIDTH } from './Slide';

const slides: Record<string, SlideData> = {
  ...
}

const nodeTypes = {
  slide: Slide,
};

const initialSlide = '0';
const { nodes, edges } = slidesToElements(initialSlide, slides)

export default function App() {
  const [currentSlide, setCurrentSlide] = useState(initialSlide);
  const { fitView } = useReactFlow();

  const handleNodeClick = useCallback<NodeMouseHandler>(
    (_, node) => {
      fitView({ nodes: [node] });
      setCurrentSlide(node.id);
    },
    [fitView],
  );

  return (
    <ReactFlow
      ...
      onNodeClick={handleNodeClick}
    />
  );
}
```

----------------------------------------

TITLE: Getting Outgoing Nodes with getOutgoers in TypeScript
DESCRIPTION: This snippet demonstrates how to use the `getOutgoers` utility from `@xyflow/react` to find all nodes that are targets of edges originating from a specified node. It requires a node object, an array of all nodes, and an array of all edges as inputs to determine the connected outgoers.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/get-outgoers.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { getOutgoers } from '@xyflow/react';

const nodes = [];
const edges = [];

const outgoers = getOutgoers(
  { id: '1', position: { x: 0, y: 0 }, data: { label: 'node' } },
  nodes,
  edges,
);
```

----------------------------------------

TITLE: Implementing Grid Layout Algorithm with slidesToElements (TypeScript)
DESCRIPTION: This TypeScript function, `slidesToElements`, converts a collection of `SlideData` objects into an array of React Flow nodes and edges. It uses a stack-based depth-first-like traversal to calculate the `x` and `y` positions for each slide based on its directional connections (`left`, `right`, `up`, `down`), ensuring a grid-like layout. It takes an initial slide ID and a map of all slides, returning the generated nodes and edges.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_8

LANGUAGE: tsx
CODE:
```
import { SlideData, SLIDE_WIDTH, SLIDE_HEIGHT } from './Slide';

export const slidesToElements = (initial: string, slides: Record<string, SlideData>) => {
  // Push the initial slide's id and the position `{ x: 0, y: 0 }` onto a stack.
  const stack = [{ id: initial, position: { x: 0, y: 0 } }];
  const visited = new Set();
  const nodes = [];
  const edges = [];

  // While that stack is not empty...
  while (stack.length) {
    // Pop the current position and slide id off the stack.
    const { id, position } = stack.pop();
    // Look up the slide data by id.
    const data = slides[id];
    const node = { id, type: 'slide', position, data };

    // Push a new node onto the nodes array with the current id, position, and slide
    // data.
    nodes.push(node);
    // add the slide's id to a set of visited slides.
    visited.add(id);

    // For every direction (left, right, up, down)...
    // Make sure the slide has not already been visited.
    if (data.left && !visited.has(data.left)) {
      // Take the current position and update the x or y coordinate by adding or
      // subtracting `SLIDE_WIDTH` or `SLIDE_HEIGHT` depending on the direction.
      const nextPosition = {
        x: position.x - SLIDE_WIDTH,
        y: position.y,
      };

      // Push the new position and the new slide's id onto a stack.
      stack.push({ id: data.left, position: nextPosition });
      // Push a new edge onto the edges array connecting the current slide to the
      // new slide.
      edges.push({ id: `${id}->${data.left}`, source: id, target: data.left });
    }

    // Repeat for the remaining directions...
  }

  return { nodes, edges };
};
```

----------------------------------------

TITLE: Using useNodesData Hook in Svelte
DESCRIPTION: This snippet demonstrates how to import and use the useNodesData hook in a Svelte component to fetch and react to changes in node data based on provided IDs.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/hooks/use-nodes-data.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang=\"ts\">\n  import { useNodesData } from '@xyflow/svelte';\n\n  const nodeData = useNodesData(['node-id-a', 'node-id-b']);\n\n  $effect(() => {\n    // nodeData changes whenever the data of the passed node ids get updated\n    console.log(nodeData.current);\n  });\n</script>
```

----------------------------------------

TITLE: Calculating Straight Path with getStraightPath in JavaScript
DESCRIPTION: This snippet demonstrates how to use the `getStraightPath` function from `@xyflow/react` to calculate an SVG path string for a straight line between two points. It also returns coordinates for a label and offset values, useful for positioning elements along the path. The function expects source and target coordinates and returns a tuple containing the path string, label X and Y coordinates, and offset X and Y values.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/get-straight-path.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { getStraightPath } from '@xyflow/react';

const source = { x: 0, y: 20 };
const target = { x: 150, y: 100 };

const [path, labelX, labelY, offsetX, offsetY] = getStraightPath({
  sourceX: source.x,
  sourceY: source.y,
  targetX: target.x,
  targetY: target.y,
});

console.log(path); //=> "M 0,20L 150,100"
console.log(labelX, labelY); //=> 75, 60
console.log(offsetX, offsetY); //=> 75, 40
```

----------------------------------------

TITLE: Using Custom Connection Line - New API (Svelte)
DESCRIPTION: Introduces the new `connectionLineComponent` prop in Svelte Flow 1.0 for specifying a custom component to render the connection line.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_15

LANGUAGE: svelte
CODE:
```
<SvelteFlow {nodes} {edges} connectionLineComponent={ConnectionLine}>
  <Background variant={BackgroundVariant.Lines} />
</SvelteFlow>
```

----------------------------------------

TITLE: Updating Nodes with Built-in Types (JavaScript)
DESCRIPTION: Modifies the `nodes` array to assign built-in `type` properties (`input` and `output`) to the nodes. These types come with predefined handle configurations, changing how edges can connect to the nodes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/getting-started/building-a-flow.mdx#_snippet_7

LANGUAGE: javascript
CODE:
```
let nodes = $state.raw([
  {
    id: '1',
    type: 'input',
    position: { x: 0, y: 0 },
    data: { label: 'Hello' },
  },
  {
    id: '2',
    type: 'output',
    position: { x: 100, y: 100 },
    data: { label: 'World' },
  },
]);
```

----------------------------------------

TITLE: Installing Project Dependencies with pnpm
DESCRIPTION: This command installs all required project dependencies using the pnpm package manager. It is a prerequisite for running the development server or building the project and should be executed after cloning the repository.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/README.md#_snippet_0

LANGUAGE: Shell
CODE:
```
$ pnpm install
```

----------------------------------------

TITLE: Using Union Types for Multiple Custom Nodes in Svelte Flow
DESCRIPTION: Illustrates how to handle multiple different custom node types within a single Svelte component by defining a union type (`NodeType`). Shows how to use type narrowing (`{#if data.type === '...'}`) within the component's template to render content based on the specific node type's data structure.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/advanced/typescript.mdx#_snippet_3

LANGUAGE: svelte
CODE:
```
<script module>
  export type NumberNodeType = Node<{ number: number }, 'number'>;
  export type TextNodeType = Node<{ text: string }, 'text'>;

  export type NodeType = NumberNodeType | TextNodeType;
</script>

<script lang="ts">
  import { Handle, Position, type NodeProps } from '@xyflow/svelte';

  let { data }: NodeProps<NodeType> = $props();
</script>

<div class="custom">
  {#if data.type === 'number'}
    <div>A special number: {data.number}</div>
  {:else}
    <div>A special text: {data.text}</div>
  {/if}
  <Handle type="source" position={Position.Right} />
</div>
```

----------------------------------------

TITLE: Using getOutgoers to Find Connected Nodes (TypeScript)
DESCRIPTION: This snippet demonstrates how to import and use the `getOutgoers` utility function from `@xyflow/svelte`. It initializes reactive state variables for nodes and edges and then calls `getOutgoers` with a sample node object, the nodes array, and the edges array to find nodes connected as targets.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/utils/get-outgoers.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { getOutgoers } from '@xyflow/svelte';

let nodes = $state.raw([]);
let edges = $state.raw([]);

const incomers = getOutgoers(
  { id: '1', position: { x: 0, y: 0 }, data: { label: 'node' } },
  nodes.value,
  edges.value,
);
```

----------------------------------------

TITLE: Adding Handles to a Custom Node (Svelte)
DESCRIPTION: This Svelte code snippet shows how to include source and target handles within a custom node component using the `Handle` component from `@xyflow/svelte`. The `type` prop specifies whether it's a 'target' or 'source' handle, and the `position` prop defines its location on the node.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/customization/custom-nodes.mdx#_snippet_3

LANGUAGE: svelte
CODE:
```
<script>
  import { Handle } from '@xyflow/svelte';
</script>

<Handle type="target" position={Position.Top} />
<Handle type="source" position={Position.Bottom} />
```

----------------------------------------

TITLE: Basic Usage of useEdges Hook in Svelte
DESCRIPTION: This snippet demonstrates how to import and use the useEdges hook within a Svelte component's script block. It calls the hook to get the current array of edges and assigns it to a variable named `edges`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/hooks/use-edges.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { useEdges } from '@xyflow/svelte';

  const edges = useEdges();
</script>
```

----------------------------------------

TITLE: Configuring Tailwind CSS for React Flow Project
DESCRIPTION: This snippet configures Tailwind CSS, specifying content files for JIT compilation, extending the default theme with custom border radii, and defining a detailed color palette using HSL variables for various UI elements like background, foreground, cards, popovers, primary, secondary, muted, accent, destructive, border, input, ring, and chart colors.
SOURCE: https://github.com/xyflow/web/blob/main/apps/example-apps/react/tutorials/components/num-node/index.html#_snippet_0

LANGUAGE: JavaScript
CODE:
```
/** @type {import('tailwindcss').Config} */
tailwind.config = {
  content: ['./index.html', './src/**/*.{
    ts,
    tsx,
    js,
    jsx
  }'],
  theme: {
    extend: {
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)'
      },
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))'
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))'
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))'
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))'
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))'
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))'
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))'
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          1: 'hsl(var(--chart-1))',
          2: 'hsl(var(--chart-2))',
          3: 'hsl(var(--chart-3))',
          4: 'hsl(var(--chart-4))',
          5: 'hsl(var(--chart-5))'
        }
      }
    }
  }
};
```

----------------------------------------

TITLE: Using reconnectEdge with useCallback in React
DESCRIPTION: This JavaScript snippet demonstrates how to integrate the `reconnectEdge` utility within a React `useCallback` hook. It defines an `onReconnect` handler that updates the `edges` state by applying the `reconnectEdge` function with the old edge, new connection, and the current array of elements, ensuring state updates are optimized.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/reconnect-edge.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
const onReconnect = useCallback(
  (oldEdge: Edge, newConnection: Connection) => setEdges((els) => reconnectEdge(oldEdge, newConnection, els)),
  []
);
```

----------------------------------------

TITLE: Customizing MiniMap Node Color by Type in Svelte
DESCRIPTION: Illustrates how to use a function for the `nodeColor` prop to dynamically set the color of each node in the minimap based on its `type`. Requires importing `SvelteFlow` and `MiniMap`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/mini-map.mdx#_snippet_2

LANGUAGE: Svelte
CODE:
```
<script lang="ts">
  import { SvelteFlow, MiniMap } from '@xyflow/svelte';
  import '@xyflow/svelte/dist/style.css';

  let nodes = $state.raw([]);
  let edges = $state.raw([]);

  function nodeColor(node) {
    return node.type === 'input' ? 'blue' : 'red';
  }
</script>

<SvelteFlow bind:nodes bind:edges>
  <MiniMap nodeColor={nodeColor} />
</SvelteFlow>
```

----------------------------------------

TITLE: Auto-Focus New Mind Map Node in React
DESCRIPTION: Employs the `useEffect` hook to automatically focus the node's input element when the component mounts. A small `setTimeout` delay is used to ensure the DOM element is ready before attempting to focus, improving user experience for new nodes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_25

LANGUAGE: jsx
CODE:
```
useEffect(() => {
  setTimeout(() => {
    if (inputRef.current) {
      inputRef.current.focus({ preventScroll: true });
    }
  }, 1);
}, []);
```

----------------------------------------

TITLE: Memoizing Node Types with useMemo (Alternative) - JSX
DESCRIPTION: This alternative implementation uses the `useMemo` hook to memoize the `nodeTypes` object. This is useful when `nodeTypes` need to be dynamically generated but should not cause re-renders unless their dependencies change. The empty dependency array `[]` ensures the object is created only once.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
import { useMemo } from 'react';
import { ReactFlow } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import MyCustomNode from './MyCustomNode';

function Flow(props) {
  const nodeTypes = useMemo(
    () => ({
      myCustomNode: MyCustomNode,
    }),
    [],
  );

  return <ReactFlow nodeTypes={nodeTypes} />;
}

export default Flow;
```

----------------------------------------

TITLE: Rendering Many Nodes and Edges in SvelteFlow
DESCRIPTION: This Svelte component generates a large number of nodes and edges programmatically and renders them using the SvelteFlow component. It initializes writable stores for nodes and edges with generated data and includes Controls and Background components for basic interaction and visualization.
SOURCE: https://github.com/xyflow/web/blob/main/apps/example-apps/svelte/examples/nodes/stress/README.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script>
  import SvelteFlow, { Controls, Background } from '@xyflow/svelte';
  import { writable } from 'svelte/store';

  const initialNodes = [];
  const initialEdges = [];
  const numNodes = 500; // Example number

  for (let i = 0; i < numNodes; i++) {
    initialNodes.push({
      id: String(i),
      position: { x: Math.random() * 800, y: Math.random() * 500 },
      data: { label: `Node ${i}` },
    });
    if (i > 0) {
      initialEdges.push({
        id: `e${i - 1}-${i}`,
        source: String(i - 1),
        target: String(i),
      });
    }
  }

  const nodes = writable(initialNodes);
  const edges = writable(initialEdges);
</script>

<div style="width: 100%; height: 500px;">
  <SvelteFlow {nodes} {edges}>
    <Controls />
    <Background />
  </SvelteFlow>
</div>

<style>
  /* Optional styles */
</style>
```

----------------------------------------

TITLE: Implement Function Bindings for State Store in Svelte Flow v1
DESCRIPTION: Provides an example of structuring a Svelte store using `$state.raw` and exporting getter/setter functions to enable function bindings when the state resides outside the component using `<SvelteFlow />`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_5

LANGUAGE: javascript
CODE:
```
// store.svelte.js

let nodes = $state.raw([...]);
let edges = $state.raw([...]);

export const getNodes = () => nodes;
export const getEdges = () => edges;
export const setNodes = (newNodes) => nodes = newNodes;
export const setEdges = (newEdges) => edges = newEdges;
```

----------------------------------------

TITLE: Use Function Bindings in Svelte Flow v1 Component
DESCRIPTION: Demonstrates how to use the getter and setter functions exported from a store (as shown in the previous snippet) to bind `nodes` and `edges` to the `<SvelteFlow />` component using the function binding syntax.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_6

LANGUAGE: svelte
CODE:
```
// BaseComponent.svelte

<script>
  import { getNodes, getEdges, setNodes, setEdges } from 'store.svelte.js';
</script>

<SvelteFlow bind:nodes={getNodes, setNodes} bind:edges={getEdges, setEdges} />
```

----------------------------------------

TITLE: Initializing React Project with Vite (Bash)
DESCRIPTION: Initialize a new React project using Vite's command-line interface, creating a standard React application structure.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/index.mdx#_snippet_1

LANGUAGE: Bash
CODE:
```
npm init vite my-react-flow-app -- --template react
```

----------------------------------------

TITLE: Implementing Keyboard Navigation with Arrow Keys in React Flow (TSX)
DESCRIPTION: This snippet defines a `handleKeyPress` callback for the `onKeyPress` event on the React Flow canvas. It listens for arrow key presses (`ArrowLeft`, `ArrowUp`, `ArrowDown`, `ArrowRight`), determines the target slide based on the current slide's connections, and if a target exists, prevents default browser scrolling, updates the `currentSlide` state, and uses `fitView` to navigate to the new slide. It relies on the `currentSlide` state and `fitView` from `useReactFlow`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_15

LANGUAGE: tsx
CODE:
```
export default function App() {
  const [currentSlide, setCurrentSlide] = useState(initialSlide);
  const { fitView } = useReactFlow();

  ...

  const handleKeyPress = useCallback<KeyboardEventHandler>(
    (event) => {
      const slide = slides[currentSlide];

      switch (event.key) {
        case 'ArrowLeft':
        case 'ArrowUp':
        case 'ArrowDown':
        case 'ArrowRight':
          const direction = event.key.slice(5).toLowerCase();
          const target = slide[direction];

          if (target) {
            event.preventDefault();
            setCurrentSlide(target);
            fitView({ nodes: [{ id: target }] });
          }
      }
    },
    [currentSlide, fitView],
  );

  return (
    <ReactFlow
      ...
      onKeyPress={handleKeyPress}
    />
  );
}
```

----------------------------------------

TITLE: Implementing moveToNextSlide with useReactFlow (TSX)
DESCRIPTION: This snippet completes the `moveToNextSlide` functionality by using the `useReactFlow` hook to access the `fitView` function. It demonstrates how to call `fitView` with a minimal node object containing only the `id` of the target slide, enabling smooth transitions between slides. The `useCallback` hook is used to memoize the function, optimizing performance.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_13

LANGUAGE: tsx
CODE:
```
import { type NodeProps, useReactFlow } from '@xyflow/react';

export function Slide({ data }: NodeProps<SlideNide>) {
  const { fitView } = useReactFlow();

  const moveToNextSlide = useCallback(
    (id: string) => fitView({ nodes: [{ id }] }),
    [fitView],
  );

  return (
    <article className="slide" style={style}>
      ...
    </article>
  );
}
```

----------------------------------------

TITLE: Customizing MiniMap Node Appearance with a Component
DESCRIPTION: This snippet demonstrates how to render custom nodes within the mini map by passing a custom component to the nodeComponent prop. The MiniMapNode function receives x and y coordinates and must return SVG elements (like <circle>) for correct rendering within the SVG context of the mini map.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/minimap.mdx#_snippet_2

LANGUAGE: jsx
CODE:
```
import { ReactFlow, MiniMap } from '@xyflow/react';

export default function Flow() {
  return (
    <ReactFlow nodes={[...]]} edges={[...]]}>
      <MiniMap nodeComponent={MiniMapNode} />
    </ReactFlow>
  );
}

function MiniMapNode({ x, y }) {
  return <circle cx={x} cy={y} r="50" />;
}
```

----------------------------------------

TITLE: Using getConnectedEdges with Svelte
DESCRIPTION: This snippet demonstrates how to import and use the `getConnectedEdges` utility function from `@xyflow/svelte`. It initializes Svelte state variables for nodes and edges and then calls the function with their values to get the connected edges.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/utils/get-connected-edges.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { getConnectedEdges } from '@xyflow/svelte';

let nodes = $state.raw([]);
let edges = $state.raw([]);

const connectedEdges = getConnectedEdges(nodes.value, edges.value);
```

----------------------------------------

TITLE: Implementing Custom Copy Action for Node Header (React Flow)
DESCRIPTION: This component provides a custom copy action button for a node header. Upon clicking, it invokes an optional `onClick` handler, passing the current node's ID and the mouse event. It uses `useNodeId` to retrieve the node's identifier, enabling external logic to handle the copy operation.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/components/nodes/node-header.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
export interface NodeHeaderCopyActionProps
  extends Omit<NodeHeaderActionProps, 'onClick'> {
  onClick?: (nodeId: string, event: React.MouseEvent) => void;
}

/**
 * A copy action button that passes the node's id to the `onClick` handler when
 * clicked.
 */
export const NodeHeaderCopyAction = React.forwardRef<
  HTMLButtonElement,
  NodeHeaderCopyActionProps
>(({ onClick, ...props }, ref) => {
  const id = useNodeId();

  const handleClick = useCallback(
    (event: React.MouseEvent) => {
      if (!onClick || !id) return;

      onClick(id, event);
    },
    [onClick],
  );

  return (
    <NodeHeaderAction
      ref={ref}
      onClick={handleClick}
      variant="ghost"
      {...props}
    >
      <Copy />
    </NodeHeaderAction>
  );
});

NodeHeaderCopyAction.displayName = 'NodeHeaderCopyAction';
```

----------------------------------------

TITLE: Defining Node Dimensions for Server-Side Rendering in React Flow (JavaScript)
DESCRIPTION: This snippet demonstrates how to explicitly define node dimensions (`width` and `height`) when rendering React Flow on the server. Since client-side measurement is not possible in an SSR environment, these properties are essential for React Flow to render nodes correctly. Alternatively, `initialWidth` and `initialHeight` can be used for dynamic dimensions that are not known in advance.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/ssr-ssg-configuration.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
const nodes = [
  {
    id: '1',
    type: 'default',
    position: { x: 0, y: 0 },
    data: { label: 'Node 1' },
    width: 100,
    height: 50,
  },
];
```

----------------------------------------

TITLE: Logging Viewport Changes with useOnViewportChange (TypeScript)
DESCRIPTION: This component demonstrates how to use the `useOnViewportChange` hook to log viewport state during different phases of interaction (start, change, end). It requires the component to be a child of a `<ReactFlowProvider />` or `<ReactFlow />` component to function correctly.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-on-viewport-change.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { useCallback } from 'react';
import { useOnViewportChange } from '@xyflow/react';

function ViewportChangeLogger() {
  useOnViewportChange({
    onStart: (viewport: Viewport) => console.log('start', viewport),
    onChange: (viewport: Viewport) => console.log('change', viewport),
    onEnd: (viewport: Viewport) => console.log('end', viewport)
  });

  return null;
}
```

----------------------------------------

TITLE: Adding Node Creation Buttons to ReactFlow Panel in JSX
DESCRIPTION: This JSX snippet defines two buttons within a `ReactFlow` `Panel` component. Each button, when clicked, triggers the `createNode` action from the store, allowing users to dynamically add 'oscillator' or 'amplifier' audio nodes to the graph.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_32

LANGUAGE: jsx
CODE:
```
<Panel position="top-right">
  <button onClick={() => store.createNode('osc')}>osc</button>
  <button onClick={() => store.createNode('amp')}>amp</button>
</Panel>
```

----------------------------------------

TITLE: Importing Tailwind CSS Directives (CSS)
DESCRIPTION: This CSS snippet replaces the default `src/index.css` content with Tailwind's base, components, and utilities directives. These directives inject Tailwind's preflight styles, component-specific styles, and utility classes into the stylesheet.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_4

LANGUAGE: css
CODE:
```
@tailwind base;
@tailwind components;
@tailwind utilities;
```

----------------------------------------

TITLE: Updating Edge with Built-in Type and Label (JavaScript)
DESCRIPTION: Modifies the `edges` array to assign a built-in `type` property (`smoothstep`) and a `label` to the edge. The `smoothstep` type renders a curved edge, and the `label` displays text along the edge.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/getting-started/building-a-flow.mdx#_snippet_8

LANGUAGE: javascript
CODE:
```
let edges = $state.raw([
  { id: 'e1-2', source: '1', target: '2', type: 'smoothstep', label: 'Hello World' },
]);
```

----------------------------------------

TITLE: Installing React Flow Dependencies (npm)
DESCRIPTION: This command installs the necessary dependencies for a React Flow project: `@xyflow/react` for the UI, `zustand` for state management, and `nanoid` for ID generation. These are essential for building interactive audio-processing graphs.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_4

LANGUAGE: bash
CODE:
```
npm install @xyflow/react zustand nanoid
```

----------------------------------------

TITLE: Defining FitViewOptions Type in TypeScript
DESCRIPTION: This TypeScript type definition outlines the available options for the `fitView` function in `xyflow`. It allows customization of viewport behavior, including `padding` around nodes, whether to `includeHiddenNodes`, `minZoom` and `maxZoom` levels, animation `duration`, and specific `nodes` to fit. These options enable fine-grained control over how the graph viewport adjusts to its content.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/fit-view-options.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type FitViewOptions = {
  padding?: number;
  includeHiddenNodes?: boolean;
  minZoom?: number;
  maxZoom?: number;
  duration?: number;
  nodes?: (Partial<Node> & { id: Node['id'] })[];
};
```

----------------------------------------

TITLE: Using ViewportPortal in Svelte
DESCRIPTION: Demonstrates the basic usage of the <ViewportPortal> component in Svelte. It shows how to import the component and place content inside it, which will then be rendered within the flow's coordinate system and affected by zoom and pan.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/viewport-portal.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
import { ViewportPortal } from '@xyflow/svelte';
</script>

<ViewportPortal>
  <div style:transform="translate(100px, 100px)" style:position="absolute">
    This div is positioned at [100, 100] on the flow.
  </div>
</ViewportPortal>
```

----------------------------------------

TITLE: Controlling Web Audio Parameters with Mouse Position in React
DESCRIPTION: This React component integrates the Web Audio nodes with user interaction. It defines an `updateValues` function that calculates frequency and gain based on mouse X and Y coordinates, respectively, and applies them to the oscillator and gain nodes. The component renders a full-screen div that triggers `updateValues` on mouse movement, allowing interactive control of the theremin.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_2

LANGUAGE: javascript
CODE:
```
import React from 'react';

const context = new AudioContext();
const osc = context.createOscillator();
const amp = context.createGain();

osc.connect(amp);
amp.connect(context.destination);

osc.start();

const updateValues = (e) => {
  const freq = (e.clientX / window.innerWidth) * 1000;
  const gain = e.clientY / window.innerHeight;

  osc.frequency.value = freq;
  amp.gain.value = gain;
};

export default function App() {
  return <div style={{ width: '100vw', height: '100vh' }} onMouseMove={updateValues} />;
}
```

----------------------------------------

TITLE: Defining Node Types Outside Component (JSX)
DESCRIPTION: This snippet provides an alternative correct approach for defining `nodeTypes` when they are static and do not change. By defining the `nodeTypes` object outside the component, it is created only once, optimizing performance and avoiding unnecessary re-renders.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_6

LANGUAGE: jsx
CODE:
```
const nodeTypes = { specialType: SpecialNode };

function Flow() {
  return <ReactFlow nodes={[]} nodeTypes={nodeTypes} />;
}
```

----------------------------------------

TITLE: Accessing New `nodeLookup` from Store (v12)
DESCRIPTION: This snippet demonstrates the updated way to access node data using `s.nodeLookup.get(id)` from the ReactFlow store in v12, replacing the old `nodeInternals` property.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_21

LANGUAGE: javascript
CODE:
```
const node = useStore((s) => s.nodeLookup.get(id));
```

----------------------------------------

TITLE: Adding a Second Node and Setting Input Type (JavaScript)
DESCRIPTION: Extends the `nodes` array to include a second node (`id: '2'`) at a different position. It also sets the `type` property of the first node (`id: '1'`) to 'input', designating it as an entry point in the flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/getting-started/building-a-flow.mdx#_snippet_2

LANGUAGE: js
CODE:
```
const nodes = [
  {
    id: '1',
    position: { x: 0, y: 0 },
    data: { label: 'Hello' },
    type: 'input',
  },
  {
    id: '2',
    position: { x: 100, y: 100 },
    data: { label: 'World' },
  },
];
```

----------------------------------------

TITLE: Calculating Node Bounds with getNodesBounds (JavaScript)
DESCRIPTION: This snippet demonstrates how to use the `getNodesBounds` function from `@xyflow/svelte` to calculate the bounding box of a list of nodes. It imports the function, defines a sample array of nodes with positions and dimensions, and then calls `getNodesBounds` with the node array to get the bounds.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/utils/get-nodes-bounds.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { getNodesBounds } from '@xyflow/svelte';

let nodes = $state.raw([
  {
    id: 'a',
    position: { x: 0, y: 0 },
    data: { label: 'a' },
    width: 50,
    height: 25,
  },
  {
    id: 'b',
    position: { x: 100, y: 100 },
    data: { label: 'b' },
    width: 50,
    height: 25,
  },
]);

const bounds = getNodesBounds(nodes.value);
```

----------------------------------------

TITLE: Calculating Viewport for Specific Bounds in React Flow (JavaScript)
DESCRIPTION: This snippet demonstrates how to use `getViewportForBounds` from `@xyflow/react` to calculate the `x`, `y`, and `zoom` values for a specified bounding box. It takes the bounds object, container width, container height, minimum zoom, and maximum zoom as arguments, returning the calculated viewport without directly modifying the current view. This is useful for server-side calculations or pre-determining viewport states.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/get-viewport-for-bounds.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { getViewportForBounds } from '@xyflow/react';

const { x, y, zoom } = getViewportForBounds(
  {
    x: 0,
    y: 0,
    width: 100,
    height: 100,
  },
  1200,
  800,
  0.5,
  2,
);
```

----------------------------------------

TITLE: Displaying Viewport State with useViewport Hook in React
DESCRIPTION: This snippet demonstrates how to use the `useViewport` hook from `@xyflow/react` to access and display the current x, y, and zoom values of the viewport. Components utilizing this hook will automatically re-render when the viewport state changes, ensuring the displayed values are always up-to-date. It requires being a child of `<ReactFlowProvider />` or `<ReactFlow />`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-viewport.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useViewport } from '@xyflow/react';

export default function ViewportDisplay() {
  const { x, y, zoom } = useViewport();

  return (
    <div>
      <p>
        The viewport is currently at ({x}, {y}) and zoomed to {zoom}.
      </p>
    </div>
  );
}
```

----------------------------------------

TITLE: Calculating Bezier Path and Label Position in xyflow/react (New API)
DESCRIPTION: This snippet demonstrates the updated `getBezierPath` function from `@xyflow/react`. It now returns the path string along with `labelX` and `labelY` coordinates for the label's position, replacing previous `centerX` and `centerY` naming conventions to better reflect its purpose.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v11.mdx#_snippet_6

LANGUAGE: jsx
CODE:
```
import { getBezierPath } from '@xyflow/react';

const [path, labelX, labelY] = getBezierPath(edgeParams);
```

----------------------------------------

TITLE: Positioning ViewportPortal Content Above Nodes in Svelte
DESCRIPTION: Illustrates how to use the `target` prop with the <ViewportPortal> component in Svelte. Setting `target="front"` ensures that the content rendered inside the portal appears above the nodes and edges in the flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/viewport-portal.mdx#_snippet_1

LANGUAGE: svelte
CODE:
```
<script lang="ts">
import { ViewportPortal } from '@xyflow/svelte';
</script>

<ViewportPortal target="front">
  <div style:transform="translate(100px, 100px)" style:position="absolute">
    This div is positioned at [100, 100] on the flow.
  </div>
</ViewportPortal>
```

----------------------------------------

TITLE: Styling React Flow Nodes by Color Mode (CSS)
DESCRIPTION: This CSS snippet shows how to apply different styles to React Flow nodes based on the active color mode (dark or light). It leverages the `.dark` and `.light` classes added to the root `.react-flow` element when the `colorMode` prop is used.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/theming.mdx#_snippet_2

LANGUAGE: css
CODE:
```
.dark .react-flow__node {
  background: #777;
  color: white;
}

.light .react-flow__node {
  background: white;
  color: #111;
}
```

----------------------------------------

TITLE: Applying `nodrag` Utility Class to Prevent Dragging
DESCRIPTION: This snippet demonstrates the use of the `nodrag` CSS class on an input element within a custom node. Applying `nodrag` prevents the default node dragging and selection behavior when interacting with elements like input fields, ensuring that user interactions target the element itself rather than the node.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/custom-nodes.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
export default function CustomNode(props: NodeProps) {
  return (
    <div>
      <input className="nodrag" type="range" min={0} max={100} />
    </div>
  );
}
```

----------------------------------------

TITLE: Handling Connection End Events in xyflow/react (New API)
DESCRIPTION: This snippet demonstrates the updated `onConnectEnd` and `onClickConnectEnd` event handlers in `@xyflow/react` (v11+), which replace the deprecated `onConnectStop` and `onClickConnectStop`. These new props provide the same functionality for handling the conclusion of connection interactions.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v11.mdx#_snippet_8

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';

const Flow = () => {
  const onConnectEnd = () => console.log('on connect stop');

  return (
    <ReactFlow
      defaultNodes={defaultNodes}
      defaultEdges={defaultEdges}
      onConnectEnd={onConnectEnd}
      onClickConnectEnd={onConnectEnd}
    />
  );
};

export default Flow;
```

----------------------------------------

TITLE: Rendering Markdown in React Flow Slide Node (TSX)
DESCRIPTION: This snippet defines a `Slide` React component used as a custom node in React Flow. It utilizes the `Remark` component from `react-remark` to render Markdown content, which is passed to the node via its `data.source` property. The component also sets fixed dimensions for the slide using `SLIDE_WIDTH` and `SLIDE_HEIGHT`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_5

LANGUAGE: tsx
CODE:
```
import { type Node, type NodeProps } from '@xyflow/react';
import { Remark } from 'react-remark';

export const SLIDE_WIDTH = 1920;
export const SLIDE_HEIGHT = 1080;

export type SlideNode = Node<SlideData, 'slide'>;

export type SlideData = {
  source: string;
};

const style = {
  width: `${SLIDE_WIDTH}px`,
  height: `${SLIDE_HEIGHT}px`,
} satisfies React.CSSProperties;

export function Slide({ data }: NodeProps<SlideNode>) {
  return (
    <article className="slide nodrag" style={style}>
      <Remark>{data.source}</Remark>
    </article>
  );
}
```

----------------------------------------

TITLE: Configuring Animated SVG Edge Data (TypeScript)
DESCRIPTION: This snippet demonstrates how to configure an `animatedSvgEdge` within the `initialEdges` array. It sets the `type` to 'animatedSvgEdge' and specifies `data` properties like `duration` for animation length and `shape` to reference a custom SVG shape defined in the `shapes` record, such as 'box'.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/components/edges/animated-svg-edge.mdx#_snippet_1

LANGUAGE: ts
CODE:
```
const initialEdges = [
  {
    // ...
    type: "animatedSvgEdge",
    data: {
      duration: 2,
      shape: "box"
    }
  } satisfies AnimatedSvgEdge,
];
```

----------------------------------------

TITLE: Defining CoordinateExtent Type in TypeScript
DESCRIPTION: This TypeScript type definition, `CoordinateExtent`, specifies a structure for representing a rectangular area. It consists of a tuple of two tuples, where each inner tuple `[number, number]` represents a coordinate point (x, y). The first point denotes the top-left corner, and the second denotes the bottom-right corner, commonly used in xyflow for defining boundaries of nodes or the viewport.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/coordinate-extent.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type CoordinateExtent = [[number, number], [number, number]];
```

----------------------------------------

TITLE: Using New `getNodesBounds` Signature with Options Object (v12)
DESCRIPTION: This snippet demonstrates the updated function signature for `getNodesBounds` in ReactFlow v12, where `nodeOrigin` is now passed within an options object as the second parameter. This change provides a more flexible and extensible API.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_16

LANGUAGE: javascript
CODE:
```
const bounds = getNodesBounds(nodes: Node[], { nodeOrigin })
```

----------------------------------------

TITLE: Defining SvelteFlow Node and Edge Type Unions (Svelte/TypeScript)
DESCRIPTION: Demonstrates how to create TypeScript type unions for custom and built-in SvelteFlow node and edge types within a Svelte component. It shows how to import necessary types, define the unions, register custom components in `nodeTypes` and `edgeTypes` objects, and initialize the SvelteFlow component with typed node and edge arrays.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/advanced/typescript.mdx#_snippet_5

LANGUAGE: svelte
CODE:
```
<script module>
  import type { BuiltInNode, BuiltInEdge } from '@xyflow/svelte';

  // Custom nodes
  import type { NumberNodeType } from './NumberNode.svelte';
  import type { TextNodeType } from './TextNode.svelte';

  // Custom edge
  import type { CustomEdgeType } from './CustomEdge.svelte';

  export type NodeType = BuiltInNode | NumberNodeType | TextNodeType;
  export type EdgeType = BuiltInEdge | CustomEdgeType;
</script>

<script lang="ts">
  import { SvelteFlow, type NodeTypes, type EdgeTypes } from '@xyflow/svelte';
  import NumberNode from './NumberNode.svelte';
  import TextNode from './TextNode.svelte';
  import CustomEdge from './CustomEdge.svelte';

  const nodeTypes: NodeTypes = {
    number: NumberNode,
    text: TextNode,
  };

  const edgeTypes: EdgeTypes = {
    custom: CustomEdge,
  };

  let nodes = $state.raw<NodeType[]>([]);
  let edges = $state.raw<EdgeType[]>([]);
</script>

<SvelteFlow bind:nodes bind:edges {nodeTypes} {edgeTypes} fitView>
  <!-- ... -->
</SvelteFlow>
```

----------------------------------------

TITLE: Disabling Pan on Drag (New API) - JSX
DESCRIPTION: This snippet demonstrates the new `panOnDrag` prop, which replaces `paneMoveable` for controlling canvas panning via dragging. Setting it to `false` disables this behavior, aligning with other pan/zoom options like `panOnScroll`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_13

LANGUAGE: jsx
CODE:
```
<ReactFlow
   ...
  panOnDrag={false}
/>
```

----------------------------------------

TITLE: Enabling Interactive Panning and Zooming for MiniMap
DESCRIPTION: This example shows how to enable user interaction with the mini map by setting the pannable and zoomable props to true. This allows users to pan and zoom the main ReactFlow viewport directly by interacting with the mini map, enhancing navigation.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/minimap.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
import { ReactFlow, MiniMap } from '@xyflow/react';

export default function Flow() {
  return (
    <ReactFlow nodes={[...]]} edges={[...]]}>
      <MiniMap pannable zoomable />
    </ReactFlow>
  );
}
```

----------------------------------------

TITLE: Using useConnection Hook in React Component
DESCRIPTION: This snippet demonstrates how to use the `useConnection` hook from `@xyflow/react` within a functional React component. It retrieves the current connection state and conditionally renders text based on whether an active connection interaction is present, showing how to access connection details like `fromNode`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-connection.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import { useConnection } from '@xyflow/react';

export default function App() {
  const connection = useConnection();

  return (
    <div>
      {connection ? `Someone is trying to make a connection from ${connection.fromNode} to this one.` : 'There are currently no incoming connections!'}
    </div>
  );
}
```

----------------------------------------

TITLE: Accessing Node Length On-Demand with useStoreApi in ReactFlow
DESCRIPTION: This example demonstrates how to use `useStoreApi` to access the internal store and retrieve the current number of nodes on-demand. It uses `useState` and `useCallback` to manage and update the displayed node count when a button is clicked, contrasting with subscription-based updates.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-store-api.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import { useState, useCallback } from 'react';
import { ReactFlow, useStoreApi } from '@xyflow/react';

const NodesLengthDisplay = () => {
  const [nodesLength, setNodesLength] = useState(0);
  const store = useStoreApi();

  const onClick = useCallback(() => {
    const { nodes } = store.getState();
    const length = nodes.length || 0;

    setNodesLength(length);
  }, [store]);

  return (
    <div>
      <p>The current number of nodes is: {nodesLength}</p>
      <button onClick={onClick}>Update node length.</button>
    </div>
  );
};

function Flow() {
  return (
    <ReactFlow nodes={nodes}>
      <NodesLengthLogger />
    </ReactFlow>
  );
}
```

----------------------------------------

TITLE: Dynamically Coloring MiniMap Nodes Based on Type
DESCRIPTION: This example illustrates how to dynamically color mini map nodes based on their node.type property using the nodeColor prop. The nodeColor function receives a Node object and returns a color string, allowing for visual differentiation of nodes within the mini map based on their category.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/minimap.mdx#_snippet_3

LANGUAGE: jsx
CODE:
```
import { ReactFlow, MiniMap } from '@xyflow/react';

export default function Flow() {
  return (
    <ReactFlow nodes={[...]]} edges={[...]]}>
      <MiniMap nodeColor={nodeColor} />
    </ReactFlow>
  );
}

function nodeColor(node) {
  switch (node.type) {
    case 'input':
      return '#6ede87';
    case 'output':
      return '#6865A5';
    default:
      return '#ff0072';
  }
}
```

----------------------------------------

TITLE: Making MiniMap Interactive in Svelte
DESCRIPTION: Shows how to enable panning and zooming on the <MiniMap /> component by setting the `pannable` and `zoomable` props to `true`. Requires importing `SvelteFlow` and `MiniMap`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/mini-map.mdx#_snippet_1

LANGUAGE: Svelte
CODE:
```
<script lang="ts">
  import { SvelteFlow, MiniMap } from '@xyflow/svelte';
  import '@xyflow/svelte/dist/style.css';

  let nodes = $state.raw([]);
  let edges = $state.raw([]);
</script>


<SvelteFlow bind:nodes bind:edges>
  <MiniMap pannable zoomable />
</SvelteFlow>
```

----------------------------------------

TITLE: Defining the SnapGrid Type in TypeScript
DESCRIPTION: This snippet defines the `SnapGrid` type as a tuple of two numbers, representing the horizontal and vertical grid size for snapping nodes on the pane.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/snapgrid.mdx#_snippet_0

LANGUAGE: ts
CODE:
```
type SnapGrid = [number, number];
```

----------------------------------------

TITLE: Accessing Deprecated `xPos` and `yPos` in Custom Nodes (v11)
DESCRIPTION: This snippet shows how `xPos` and `yPos` were accessed as props in custom ReactFlow nodes in v11. These properties have been renamed to provide more descriptive absolute position information.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_13

LANGUAGE: javascript
CODE:
```
function CustomNode({ xPos, yPos }) {
  ...
}
```

----------------------------------------

TITLE: Handling Edge Creation - Old API (Svelte)
DESCRIPTION: Shows the previous event handler `onEdgeCreate` used to intercept and potentially modify a new edge object before it was added to the flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_16

LANGUAGE: svelte
CODE:
```
<SvelteFlow
  {nodes}
  {edges}
  onEdgeCreate={(connection) => ({...connection, id: crypto.randomUUID()})}
/>
```

----------------------------------------

TITLE: Using useNodeConnections Hook in Svelte
DESCRIPTION: This snippet demonstrates how to use the `useNodeConnections` hook in a Svelte component to get incoming connections for a specific target handle with ID 'my-handle'. It then displays the number of connections.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/hooks/use-node-connections.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script>\n  import { useNodeConnections } from '@xyflow/svelte';\n  const connections = useNodeConnections({ handleType: 'target', handleId: 'my-handle' });\n</script>\n\n<div>There are currently {connections.length} incoming connections!</div>
```

----------------------------------------

TITLE: Typing useStore Selector with TypeScript (TSX)
DESCRIPTION: This snippet shows how to properly type the selector function used with `useStore` in TypeScript. It demonstrates passing a generic type `ReactFlowState<CustomNodeType>` to the selector to ensure type safety when accessing properties like `s.nodes` from the React Flow state.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-store.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
const nodes = useStore((s: ReactFlowState<CustomNodeType>) => ({
  nodes: s.nodes,
}));
```

----------------------------------------

TITLE: Defining Position Enum in TypeScript
DESCRIPTION: This TypeScript enum defines four cardinal directions: Left, Top, Right, and Bottom. It is used within the xyflow library to specify less precise positions, typically for edges and handles, by mapping string values to each direction.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/position.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export enum Position {
  Left = 'left',
  Top = 'top',
  Right = 'right',
  Bottom = 'bottom'
}
```

----------------------------------------

TITLE: Installing Svelte Flow Dependency (npm)
DESCRIPTION: Command to install the Svelte Flow package using npm. This is the primary dependency required to use the library in a project.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/index.mdx#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @xyflow/svelte
```

----------------------------------------

TITLE: Installing Svelte Flow in Project (npm)
DESCRIPTION: Command to install the Svelte Flow package using npm within an existing Svelte project directory. This step follows project creation.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/index.mdx#_snippet_3

LANGUAGE: bash
CODE:
```
npm install @xyflow/svelte
```

----------------------------------------

TITLE: Typing useStoreApi with Custom Node and Edge Types in TypeScript
DESCRIPTION: This snippet illustrates how to apply generic type arguments to the `useStoreApi` hook for improved type safety. By specifying `CustomNodeType` and `CustomEdgeType`, developers can ensure that the accessed store state conforms to their custom data structures for nodes and edges.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-store-api.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
const store = useStoreApi<CustomNodeType, CustomEdgeType>();
```

----------------------------------------

TITLE: Styling React Flow MiniMap with Styled Components (JSX)
DESCRIPTION: This example illustrates how to style the <MiniMap /> component using Styled Components. It wraps the MiniMap component to apply custom background colors and style specific internal elements like the minimap mask and nodes, leveraging theme props for dynamic styling.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/theming.mdx#_snippet_6

LANGUAGE: jsx
CODE:
```
import { MiniMap } from '@xyflow/react';

const StyledMiniMap = styled(MiniMap)`
  background-color: ${(props) => props.theme.bg};

  .react-flow__minimap-mask {
    fill: ${(props) => props.theme.minimapMaskBg};
  }

  .react-flow__minimap-node {
    fill: ${(props) => props.theme.nodeBg};
    stroke: none;
  }
`;
```

----------------------------------------

TITLE: Checking if an Object is a Node in React Flow (JavaScript)
DESCRIPTION: This snippet demonstrates how to use the `isNode` utility from `@xyflow/react` to check if a given object conforms to the `Node` type. It defines a sample object `node` and then uses `isNode()` within an `if` condition to perform the type check, which acts as a type guard in TypeScript.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/is-node.mdx#_snippet_0

LANGUAGE: js
CODE:
```
import { isNode } from '@xyflow/react';

const node = {
  id: 'node-a',
  data: {
    label: 'node',
  },
  position: {
    x: 0,
    y: 0,
  },
};

if (isNode(node)) {
  // ..
}
```

----------------------------------------

TITLE: Using getBezierPath in Svelte
DESCRIPTION: Demonstrates how to import and use the getBezierPath utility from @xyflow/svelte to calculate a bezier path between two points. It shows how to provide source and target coordinates and positions, and logs the resulting SVG path string, label coordinates, and offsets.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/utils/get-bezier-path.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { Position, getBezierPath } from '@xyflow/svelte';

const source = { x: 0, y: 20 };
const target = { x: 150, y: 100 };

const [path, labelX, labelY, offsetX, offsetY] = getBezierPath({
  sourceX: source.x,
  sourceY: source.y,
  sourcePosition: Position.Right,
  targetX: target.x,
  targetY: target.y,
  targetPosition: Position.Left,
});

console.log(path); //=> "M0,20 C75,20 75,100 150,100"
console.log(labelX, labelY); //=> 75, 60
console.log(offsetX, offsetY); //=> 75, 40
```

----------------------------------------

TITLE: Adding React Flow Component with shadcn CLI
DESCRIPTION: This command demonstrates how to add a pre-built React Flow component, specifically a database schema node, to a project using the `npx shadcn add` command. It leverages the shadcn CLI to vendor component source code directly into the user's application, allowing for full customizability and integration.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/src/content/react-flow-components.mdx#_snippet_0

LANGUAGE: shell
CODE:
```
npx shadcn add https://ui.reactflow.dev/database-schema-node
```

----------------------------------------

TITLE: Defining MarkerType Enum in TypeScript
DESCRIPTION: This TypeScript enum defines the `MarkerType` with two built-in values: `Arrow` and `ArrowClosed`. These types are used in Svelte Flow to specify the visual style of markers at the start or end of an edge, configured via the `markerStart` or `markerEnd` edge options.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/marker-type.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export enum MarkerType {
  Arrow = 'arrow',
  ArrowClosed = 'arrowclosed',
}
```

----------------------------------------

TITLE: Defining CoordinateExtent Type in TypeScript
DESCRIPTION: This TypeScript type alias defines `CoordinateExtent` as a tuple of two tuples, where each inner tuple represents a point `[x, y]`. It's used to specify a rectangular area, typically for bounding boxes of nodes or the viewport in xyflow applications.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/coordinate-extent.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
export type CoordinateExtent = [[number, number], [number, number]];
```

----------------------------------------

TITLE: Layering Multiple Line Backgrounds in ReactFlow (TSX)
DESCRIPTION: This example illustrates how to combine multiple Background components to create a layered grid effect. Two line backgrounds are rendered: one with a 10px gap and another with a 100px gap, creating an accented grid. Each Background component must have a unique id prop when layered.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/background.mdx#_snippet_1

LANGUAGE: TSX
CODE:
```
import { ReactFlow, Background, BackgroundVariant } from '@xyflow/react';

import '@xyflow/react/dist/style.css';

export default function Flow() {
  return (
    <ReactFlow defaultNodes={[...]} defaultEdges={[...]}>
      <Background
        id="1"
        gap={10}
        color="#f1f1f1"
        variant={BackgroundVariant.Lines}
      />

      <Background
        id="2"
        gap={100}
        color="#ccc"
        variant={BackgroundVariant.Lines}
      />
    </ReactFlow>
  );
}
```

----------------------------------------

TITLE: Triggering Store Actions with useStore (JSX)
DESCRIPTION: This example illustrates how to use `useStore` to access and trigger internal React Flow actions. It selects the `setMinZoom` action from the store and uses it to create a button that, when clicked, sets the minimum zoom level of the React Flow component to 6.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-store.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
import { useStore } from '@xyflow/react';

const setMinZoomSelector = (state) => state.setMinZoom;

function MinZoomSetter() {
  const setMinZoom = useStore(setMinZoomSelector);

  return <button onClick={() => setMinZoom(6)}>set min zoom</button>;
}
```

----------------------------------------

TITLE: Calculating Viewport for Bounds using getViewportForBounds (JavaScript)
DESCRIPTION: This snippet demonstrates how to import and use the getViewportForBounds function from the @xyflow/svelte library. It calculates the optimal viewport (x, y, zoom) for a specified bounding box within a container of given dimensions, considering minimum and maximum zoom levels.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/utils/get-viewport-for-bounds.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { getViewportForBounds } from '@xyflow/svelte';

const { x, y, zoom } = getViewportForBounds(
  {
    x: 0,
    y: 0,
    width: 100,
    height: 100,
  },
  1200,
  800,
  0.5,
  2,
);
```

----------------------------------------

TITLE: Adjust Node Width with Label Length in React
DESCRIPTION: Uses the `useLayoutEffect` hook to synchronously update the width of a node's input element based on the length of the `data.label` property. This ensures the node size adjusts immediately after the label changes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_24

LANGUAGE: jsx
CODE:
```
useLayoutEffect(() => {
  if (inputRef.current) {
    inputRef.current.style.width = `${data.label.length * 8}px`;
  }
}, [data.label.length]);
```

----------------------------------------

TITLE: Defining Custom Node/Edge Props - New API (Svelte)
DESCRIPTION: Shows the new approach for defining and accessing props in custom Svelte Flow nodes or edges leveraging Svelte 5's `$props()` rune for reactive props.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_8

LANGUAGE: svelte
CODE:
```
let { data, position, selected } : NodeProps = $props();
```

----------------------------------------

TITLE: Calculating Node Bounds with getNodesBounds (JavaScript)
DESCRIPTION: This snippet demonstrates how to use the `getNodesBounds` function from `@xyflow/react` to calculate the collective bounding box for an array of nodes. It initializes two nodes with specific positions, dimensions, and data, then passes this array to `getNodesBounds` to determine the overall bounds.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/get-nodes-bounds.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { getNodesBounds } from '@xyflow/react';

const nodes = [
  {
    id: 'a',
    position: { x: 0, y: 0 },
    data: { label: 'a' },
    width: 50,
    height: 25,
  },
  {
    id: 'b',
    position: { x: 100, y: 100 },
    data: { label: 'b' },
    width: 50,
    height: 25,
  },
];

const bounds = getNodesBounds(nodes);
```

----------------------------------------

TITLE: Generating Smooth Step Path with getSmoothStepPath in JavaScript
DESCRIPTION: This snippet demonstrates how to use the `getSmoothStepPath` utility from `@xyflow/react` to calculate a smooth stepped path between two points. It defines source and target coordinates and positions, then destructures the returned tuple to get the SVG path string, label coordinates, and offset values. The output shows example values for the generated path and coordinates.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/get-smooth-step-path.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { Position, getSmoothStepPath } from '@xyflow/react';

const source = { x: 0, y: 20 };
const target = { x: 150, y: 100 };

const [path, labelX, labelY, offsetX, offsetY] = getSmoothStepPath({
  sourceX: source.x,
  sourceY: source.y,
  sourcePosition: Position.Right,
  targetX: target.x,
  targetY: target.y,
  targetPosition: Position.Left,
});

console.log(path); //=> "M0 20L20 20L 70,20Q 75,20 75,25L 75,95Q ..."
console.log(labelX, labelY); //=> 75, 60
console.log(offsetX, offsetY); //=> 75, 40
```

----------------------------------------

TITLE: Rendering Edge Labels - New API (Svelte)
DESCRIPTION: Shows the new `<EdgeLabel>` component for rendering edge labels, which simplifies positioning with `x` and `y` props and includes built-in click handling.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_19

LANGUAGE: svelte
CODE:
```
// CustomEdge.svelte
<BaseEdge path={path} />
<EdgeLabel x={labelX} y={labelY} selectEdgeOnClick>
  <div>My Edge Label</div>
</EdgeLabel>
```

----------------------------------------

TITLE: Disabling d3-drag for Jest Mouse Event Testing in React Flow (JavaScript)
DESCRIPTION: This JavaScript snippet demonstrates how to disable `d3-drag` within a React Flow component by setting `nodesDraggable` and `panOnDrag` to `false`. This is necessary when testing mouse events in custom nodes with Jest, as `d3-drag` is designed for browser environments and does not function correctly outside of them.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/testing.mdx#_snippet_1

LANGUAGE: JavaScript
CODE:
```
<ReactFlow nodesDraggable={false} panOnDrag={false} {...rest} />
```

----------------------------------------

TITLE: Calculating Bezier Path for Edges - JavaScript
DESCRIPTION: This snippet demonstrates how to use `getBezierPath` from `@xyflow/react` to calculate the SVG path string, label coordinates, and offset values for a Bezier edge between two specified points. It requires `Position` and `getBezierPath` as dependencies from the `@xyflow/react` library.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/get-bezier-path.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { Position, getBezierPath } from '@xyflow/react';

const source = { x: 0, y: 20 };
const target = { x: 150, y: 100 };

const [path, labelX, labelY, offsetX, offsetY] = getBezierPath({
  sourceX: source.x,
  sourceY: source.y,
  sourcePosition: Position.Right,
  targetX: target.x,
  targetY: target.y,
  targetPosition: Position.Left,
});

console.log(path); //=> "M0,20 C75,20 75,100 150,100"
console.log(labelX, labelY); //=> 75, 60
console.log(offsetX, offsetY); //=> 75, 40
```

----------------------------------------

TITLE: Calculating Simple Bezier Path in React Flow (JavaScript)
DESCRIPTION: This snippet demonstrates how to use the `getSimpleBezierPath` utility from `@xyflow/react` to calculate the SVG path string and label coordinates for a simple Bezier edge. It takes source and target node positions and returns the path data, label X/Y coordinates, and offset values for rendering.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/get-simple-bezier-path.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { Position, getSimpleBezierPath } from '@xyflow/react';

const source = { x: 0, y: 20 };
const target = { x: 150, y: 100 };

const [path, labelX, labelY, offsetX, offsetY] = getSimpleBezierPath({
  sourceX: source.x,
  sourceY: source.y,
  sourcePosition: Position.Right,
  targetX: target.x,
  targetY: target.y,
  targetPosition: Position.Left,
});

console.log(path); //=> "M0,20 C75,20 75,100 150,100"
console.log(labelX, labelY); //=> 75, 60
console.log(offsetX, offsetY); //=> 75, 40
```

----------------------------------------

TITLE: Installing @xyflow/react package
DESCRIPTION: This snippet shows how to install the new `@xyflow/react` package using npm, which replaces the old `reactflow` package for React Flow 12. This is the first step in migrating your project.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @xyflow/react
```

----------------------------------------

TITLE: Installing React Flow and React Remark Dependencies
DESCRIPTION: This command installs the core `@xyflow/react` library for building node-based UIs and `react-remark` for rendering markdown content. These are essential dependencies for the presentation application being built.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_1

LANGUAGE: bash
CODE:
```
npm install @xyflow/react react-remark
```

----------------------------------------

TITLE: Defining the Position Enum in TypeScript
DESCRIPTION: This TypeScript enum defines the four cardinal directions: Left, Top, Right, and Bottom. It is used in xyflow to specify the position of elements, particularly in relation to edges and handles, providing a less precise placement than `PanelPosition`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/position.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export enum Position {
  Left = 'left',
  Top = 'top',
  Right = 'right',
  Bottom = 'bottom'
}
```

----------------------------------------

TITLE: Implementing Custom Output Node with Audio Toggle (React/JSX)
DESCRIPTION: This React/JSX snippet defines a custom `Out` node component for React Flow. It uses a selector to retrieve `isRunning` state and `toggleAudio` action from the Zustand store, rendering a button that visually indicates and controls the audio processing state. It also includes a `Handle` for connections.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_17

LANGUAGE: JSX
CODE:
```
import React from 'react';
import { Handle } from '@xyflow/react';
import { shallow } from 'zustand/shallow';
import { useStore } from '../store';

const selector = (store) => ({
  isRunning: store.isRunning,
  toggleAudio: store.toggleAudio,
});

export default function Out({ id, data }) {
  const { isRunning, toggleAudio } = useStore(selector, shallow);

  return (
    <div>
      <Handle type="target" position="top" />

      <div>
        <p>Output Node</p>

        <button onClick={toggleAudio}>
          {isRunning ? (
            <span role="img" aria-label="mute">
              🔇
            </span>
          ) : (
            <span role="img" aria-label="unmute">
              🔈
            </span>
          )}
        </button>
      </div>
    </div>
  );
}
```

----------------------------------------

TITLE: Importing and Using useNodes Hook in Svelte
DESCRIPTION: This snippet demonstrates how to import the useNodes hook from the @xyflow/svelte library and call it within a Svelte component's script block. The hook returns a reactive store containing the current array of nodes, which can then be used within the component template or further script logic.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/hooks/use-nodes.mdx#_snippet_0

LANGUAGE: svelte
CODE:
```
<script lang="ts">
  import { useNodes } from '@xyflow/svelte';

  const nodes = useNodes();
</script>
```

----------------------------------------

TITLE: Adding Navigation Buttons to Slide Component (TSX)
DESCRIPTION: This snippet demonstrates how to add directional navigation buttons to the `<footer>` of a `Slide` component. It conditionally renders buttons based on available connected slides (e.g., `data.left`, `data.up`) and sets up a placeholder `moveToNextSlide` callback. The `"nopan"` class is crucial for preventing canvas panning when interacting with these buttons.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_11

LANGUAGE: tsx
CODE:
```
import { type NodeProps, fitView } from '@xyflow/react';
import { Remark } from 'react-remark';
import { useCallback } from 'react';

...

export function Slide({ data }: NodeProps<SlideNide>) {
  const moveToNextSlide = useCallback((id: string) => {}, []);

  return (
    <article className="slide nodrag" style={style}>
      <Remark>{data.source}</Remark>
      <footer className="slide__controls nopan">
        {data.left && (<button onClick={() => moveToNextSlide(data.left)}>←</button>)}
        {data.up && (<button onClick={() => moveToNextSlide(data.up)}>↑</button>)}
        {data.down && (<button onClick={() => moveToNextSlide(data.down)}>↓</button>)}
        {data.right && (<button onClick={() => moveToNextSlide(data.right)}>→</button>)}
      </footer>
    </article>
  );
}
```

----------------------------------------

TITLE: Defining the Handle Type in TypeScript
DESCRIPTION: This TypeScript type definition outlines the structure for a `Handle` object within the `xyflow` library. It specifies essential attributes such as `id` (optional), `nodeId` (the ID of the associated node), `x` and `y` coordinates, `position` (e.g., `Position.Top`), `type` (either 'source' or 'target' for connections), and `width`/`height` for its dimensions. This type is crucial for managing interactive connection points on nodes in a flow diagram.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/handle.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
export type Handle = {
  id?: string | null;
  nodeId: string;
  x: number;
  y: number;
  position: Position;
  type: 'source' | 'target';
  width: number;
  height: number;
};
```

----------------------------------------

TITLE: Accessing Store API with useStoreApi Hook (JavaScript)
DESCRIPTION: This snippet shows how to use the `useStoreApi` hook to get the React Flow store instance. This is particularly useful for accessing store state within event handlers without causing component re-renders, as it provides a stable reference to the store.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_9

LANGUAGE: js
CODE:
```
import { useStoreApi } from 'react-flow-renderer';

...

const store = useStoreApi();

...
// in an event handler
const [x, y, zoom] = store.getState().transform;
```

----------------------------------------

TITLE: Defining Nodes with New `parentId` in ReactFlow (v12)
DESCRIPTION: This snippet demonstrates the updated way to define nodes in ReactFlow v12, using the `parentId` property instead of `parentNode` to link a node to its parent in a subflow. This change improves clarity and accuracy.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_12

LANGUAGE: javascript
CODE:
```
const nodes = [
  // some nodes ...
  {
    id: 'xyz-id',
    position: { x: 0, y: 0 },
    type: 'default',
    data: {},
    parentId: 'abc-id',
  },
];
```

----------------------------------------

TITLE: Adding Temporary Initial Nodes to Zustand Store in JavaScript
DESCRIPTION: This snippet shows how to temporarily add initial dummy nodes to the Zustand store for demonstration purposes. It directly initializes the `nodes` array within the `createWithEqualityFn` call, providing a starting set of nodes for the React Flow graph. This is useful for testing or initial setup before dynamic node creation is implemented.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_9

LANGUAGE: javascript
CODE:
```
const useStore = createWithEqualityFn((set, get) => ({
  nodes: [
    { id: 'a', data: { label: 'oscillator' }, position: { x: 0, y: 0 } },
    { id: 'b', data: { label: 'gain' }, position: { x: 50, y: 50 } },
    { id: 'c', data: { label: 'output' }, position: { x: -50, y: 100 } }
  ],
  ...
}));
```

----------------------------------------

TITLE: Defining Initial Oscillator Node Component (React/JSX)
DESCRIPTION: This snippet defines the initial React component for an Oscillator node in React Flow. It includes a basic structure with controls for frequency and waveform type, and a source handle for connections. The input values are static, reflecting the initial `data` prop.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_10

LANGUAGE: jsx
CODE:
```
import React from 'react';
import { Handle } from '@xyflow/react';

import { useStore } from '../store';

export default function Osc({ id, data }) {
  return (
    <div>
      <div>
        <p>Oscillator Node</p>

        <label>
          <span>Frequency</span>
          <input
            className="nodrag"
            type="range"
            min="10"
            max="1000"
            value={data.frequency} />
          <span>{data.frequency}Hz</span>
        </label>

        <label>
          <span>Waveform</span>
          <select className="nodrag" value={data.type}>
            <option value="sine">sine</option>
            <option value="triangle">triangle</option>
            <option value="sawtooth">sawtooth</option>
            <option value="square">square</option>
          </select>
      </div>

      <Handle type="source" position="bottom" />
    </div>
  );
};
```

----------------------------------------

TITLE: Integrating Custom Layout Algorithm in React Flow (TSX)
DESCRIPTION: This snippet demonstrates how to integrate a custom `slidesToElements` function to generate nodes and edges for a React Flow instance. It defines static slide data, registers a custom `Slide` node type, and configures `ReactFlow` to display the generated layout, ensuring the initial slide is focused on load.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_9

LANGUAGE: tsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
import { slidesToElements } from './slides';
import { Slide, SlideData, SLIDE_WIDTH } from './Slide';

const slides: Record<string, SlideData> = {
  '0': { source: '# Hello, React Flow!', right: '1' },
  '1': { source: '...', left: '0', right: '2' },
  '2': { source: '...', left: '1' }
};

const nodeTypes = {
  slide: Slide
};

const initialSlide = '0';
const { nodes, edges } = slidesToElements(initialSlide, slides);

export default function App() {
  return (
    <ReactFlow
      nodes={nodes}
      nodeTypes={nodeTypes}
      fitView
      fitViewOptions={{ nodes: [{ id: initialSlide }] }}
      minZoom={0.1}
    />
  );
}
```

----------------------------------------

TITLE: Using New `onReconnect` API in ReactFlow (v12)
DESCRIPTION: This snippet shows the updated `onReconnect` API for handling edge reconnections in ReactFlow v12, replacing the old `onEdgeUpdate` functions. It includes `onReconnectStart` and `onReconnectEnd` for managing the reconnection lifecycle.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_10

LANGUAGE: javascript
CODE:
```
<ReactFlow
  onReconnect={onReconnect}
  onReconnectStart={onReconnectStart}
  onReconnectEnd={onReconnectEnd}
/>
```

----------------------------------------

TITLE: Styling Custom Node Elements (Initial)
DESCRIPTION: Provides initial CSS styles for the `inputWrapper`, `dragHandle`, and `input` elements within the custom `MindMapNode` component to control layout and appearance.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_21

LANGUAGE: css
CODE:
```
.inputWrapper {
  display: flex;
  height: 20px;
  z-index: 1;
  position: relative;
}

.dragHandle {
  background: transparent;
  width: 14px;
  height: 100%;
  margin-right: 4px;
  display: flex;
  align-items: center;
}

.input {
  border: none;
  padding: 0 2px;
  border-radius: 1px;
  font-weight: 700;
  background: transparent;
  height: 100%;
  color: #222;
}
```

----------------------------------------

TITLE: Defining Connection Line Types (TypeScript)
DESCRIPTION: Defines the available types for connection lines in Svelte Flow. These values are used with the `connectionLineType` prop on the `<SvelteFlow />` component to specify the visual style of connections.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/connection-line-type.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
export enum ConnectionLineType {
  Bezier = 'default',
  Straight = 'straight',
  Step = 'step',
  SmoothStep = 'smoothstep',
  SimpleBezier = 'simplebezier',
}
```

----------------------------------------

TITLE: Adding DataEdge Component via shadcn/ui
DESCRIPTION: This command demonstrates how to add the `data-edge` component to a project using `npx shadcn@latest add`. This component is provided by `ui.reactflow.dev` and is used to visualize data on edges in a React Flow application.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_20

LANGUAGE: Bash
CODE:
```
npx shadcn@latest add https://ui.reactflow.dev/data-edge
```

----------------------------------------

TITLE: Basic MiniMap Usage in SvelteFlow
DESCRIPTION: Demonstrates how to import and render the <MiniMap /> component within a <SvelteFlow /> instance, binding nodes and edges. Shows a basic prop `nodeStrokeWidth`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/mini-map.mdx#_snippet_0

LANGUAGE: Svelte
CODE:
```
<script lang="ts">
  import { SvelteFlow, MiniMap } from '@xyflow/svelte';
  import '@xyflow/svelte/dist/style.css';

  let nodes = $state.raw([]);
  let edges = $state.raw([]);
</script>

<SvelteFlow bind:nodes bind:edges>
  <MiniMap nodeStrokeWidth={3} />
</SvelteFlow>
```

----------------------------------------

TITLE: Defining PanelPosition Type in TypeScript
DESCRIPTION: This TypeScript type defines a set of predefined string literal values used to specify the position of UI panels or components within a viewport. It enumerates common cardinal and intercardinal directions, enabling consistent placement for elements like MiniMap and Controls.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/panel-position.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type PanelPosition =
  | 'top-left'
  | 'top-center'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-center'
  | 'bottom-right';
```

----------------------------------------

TITLE: Defining MiniMapNodeProps Type in TypeScript
DESCRIPTION: This TypeScript type definition outlines the properties required for a node within the MiniMap component. It includes `id`, `x`, `y`, `width`, `height` for positioning and sizing, `borderRadius`, `className`, `color`, `shapeRendering`, `strokeColor`, `strokeWidth`, and `style` for styling. It also includes `selected` status and an optional `onClick` handler for interactivity.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/mini-map-node-props.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type MiniMapNodeProps = {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  borderRadius: number;
  className: string;
  color: string;
  shapeRendering: string;
  strokeColor: string;
  strokeWidth: number;
  style?: CSSProperties;
  selected: boolean;
  onClick?: (event: MouseEvent, id: string) => void;
};
```

----------------------------------------

TITLE: Handling Edge Creation - New API (Svelte)
DESCRIPTION: Introduces the renamed event handler `onbeforeconnect`, which serves the same purpose as `onEdgeCreate` but aligns better with other 'before' events in the API.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_17

LANGUAGE: svelte
CODE:
```
<SvelteFlow
  bind:nodes
  bind:edges
  onbeforeconnect={(connection) => ({ ...connection, id: crypto.randomUUID() })}>
</SvelteFlow>
```

----------------------------------------

TITLE: Defining KeyDefinition Types in TypeScript
DESCRIPTION: This TypeScript snippet defines the `KeyDefinitionObject` type, which specifies a key (string) and an optional modifier (`KeyModifier`), and the `KeyDefinition` type, which can be either a string or a `KeyDefinitionObject`. These types are used for configuring built-in keybindings like `selectionKey` or `deleteKey`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/key-definition.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type KeyDefinitionObject = { key: string; modifier?: KeyModifier };
export type KeyDefinition = string | KeyDefinitionObject;
```

----------------------------------------

TITLE: Adding React Flow Components with shadcn CLI
DESCRIPTION: This command uses the `shadcn` CLI to add `node-header` and `labeled-handle` components from `ui.reactflow.dev`. These components provide building blocks for creating custom React Flow nodes, and their installation also includes standard `shadcn/ui` dependencies.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_16

LANGUAGE: bash
CODE:
```
npx shadcn@latest add \
  https://ui.reactflow.dev/node-header \
  https://ui.reactflow.dev/labeled-handle
```

----------------------------------------

TITLE: Using useNodesInitialized Hook in Svelte
DESCRIPTION: This snippet demonstrates how to import and call the `useNodesInitialized` hook within a Svelte component's script block. It shows the basic syntax for using the hook to get the initialization status.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/hooks/use-nodes-initialized.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
<script lang="ts">
  import { useNodesInitialized } from '@xyflow/svelte';

  const useNodesInitialized = useNodesInitialized();
</script>
```

----------------------------------------

TITLE: Hardcoding Web Audio API Nodes and Connections (JavaScript)
DESCRIPTION: This JavaScript snippet from `./src/audio.js` demonstrates how to hardcode and initialize specific Web Audio API nodes. It creates an `OscillatorNode`, a `GainNode`, and references the `AudioContext` destination, then stores these nodes in the `nodes` Map using predefined IDs for later management and connection within the audio graph.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_19

LANGUAGE: JavaScript
CODE:
```
const context = new AudioContext();
const nodes = new Map();

const osc = context.createOscillator();
osc.frequency.value = 220;
osc.type = 'square';
osc.start();

const amp = context.createGain();
amp.gain.value = 0.5;

const out = context.destination;

nodes.set('a', osc);
nodes.set('b', amp);
nodes.set('c', out);
```

----------------------------------------

TITLE: Integrating Edge Connection with Store in JavaScript
DESCRIPTION: This `addEdge` action in the Zustand store handles the creation of new edges. After any internal store logic for adding the edge, it calls the `connect` function from `./audio.js` to establish the actual audio connection between the source and target nodes based on the edge data.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_26

LANGUAGE: javascript
CODE:
```
import { ..., connect } from './audio';

export const useStore = createWithEqualityFn((set, get) => ({
  ...

  addEdge(data) {
    ...

    connect(data.source, data.target);
  },

  ...
}));
```

----------------------------------------

TITLE: Styling Custom Mind Map Node - CSS
DESCRIPTION: Provides CSS rules specifically for the custom 'mindmap' node class (`.react-flow__node-mindmap`). These styles define the visual appearance of the node, including background color, border, border radius, padding, and font weight, making it distinct from default nodes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_9

LANGUAGE: css
CODE:
```
.react-flow__node-mindmap {
  background: white;
  border-radius: 2px;
  border: 1px solid transparent;
  padding: 2px 5px;
  font-weight: 700;
}
```

----------------------------------------

TITLE: Bind Nodes/Edges with bind:prop in Svelte Flow v1 (New API)
DESCRIPTION: Shows the updated syntax for binding `nodes` and `edges` to the `<SvelteFlow />` component using Svelte's `bind:` directive, required in version 1.0 for two-way data flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_4

LANGUAGE: svelte
CODE:
```
<SvelteFlow bind:nodes bind:edges />
```

----------------------------------------

TITLE: Adding updateNode Action to Zustand Store (JavaScript)
DESCRIPTION: This snippet extends the Zustand store with an `updateNode` action. This action allows for partial updates to a node's data by mapping over the existing nodes and merging new data into the target node's `data` property. It's crucial for making custom node controls interactive.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_11

LANGUAGE: js
CODE:
```
export const useStore = createWithEqualityFn((set, get) => ({
  ...

  updateNode(id, data) {
    set({
      nodes: get().nodes.map(node =>
        node.id === id
          ? { ...node, data: { ...node.data, ...data } }
          : node
      )
    });
  },

  ...
}));
```

----------------------------------------

TITLE: Defining InternalNodeBase Type in TypeScript
DESCRIPTION: This TypeScript type definition extends a base `NodeType` with internal properties essential for React Flow's rendering. It includes `measured` properties for width and height, and `internals` such as `positionAbsolute`, `z-index`, the original `userNode`, `handleBounds`, and general `bounds`. This structure is used internally by React Flow to manage node rendering and layout.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/internal-node.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type InternalNodeBase<NodeType extends NodeBase = NodeBase> =
  NodeType & {
    measured: {
      width?: number;
      height?: number;
    };
    internals: {
      positionAbsolute: XYPosition;
      z: number;
      userNode: NodeType;
      handleBounds?: NodeHandleBounds;
      bounds?: NodeBounds;
    };
  };
```

----------------------------------------

TITLE: Defining Hidden Node (New API) - JavaScript
DESCRIPTION: This snippet shows the new `hidden` boolean property for nodes, replacing `isHidden`. Setting it to `true` hides the node, aligning with the non-prefixed naming convention now used for all node and edge boolean options.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_17

LANGUAGE: js
CODE:
```
const hiddenNode = { id: '1', hidden: true, position: { x: 50, y: 50 } };
```

----------------------------------------

TITLE: Defining SlideData Type for Directional Navigation (TypeScript)
DESCRIPTION: This TypeScript type definition extends the `SlideData` structure to include optional properties (`left`, `up`, `down`, `right`) that store the IDs of adjacent slides. This enables a declarative way to define slide relationships, facilitating the computation of a grid layout and future navigation features without directly managing nodes and edges.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_7

LANGUAGE: tsx
CODE:
```
export type SlideData = {
  source: string;
  left?: string;
  up?: string;
  down?: string;
  right?: string;
};
```

----------------------------------------

TITLE: Refining Custom Node Styling (Focus/Pointer Events)
DESCRIPTION: Updates CSS styles for the custom node elements and connection line. It sets `pointer-events: none` by default for the input and wrapper to allow connections to pass through, but enables pointer events for the `dragHandle` and the input when it is focused.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_23

LANGUAGE: css
CODE:
```
/* we want the connection line to be below the node */
.react-flow .react-flow__connectionline {
  z-index: 0;
}

/* pointer-events: none so that the click for the connection goes through */
.inputWrapper {
  display: flex;
  height: 20px;
  position: relative;
  z-index: 1;
  pointer-events: none;
}

/* pointer-events: all so that we can use the drag handle (here the user cant start a new connection) */
.dragHandle {
  background: transparent;
  width: 14px;
  height: 100%;
  margin-right: 4px;
  display: flex;
  align-items: center;
  pointer-events: all;
}

/* pointer-events: none by default */
.input {
  border: none;
  padding: 0 2px;
  border-radius: 1px;
  font-weight: 700;
  background: transparent;
  height: 100%;
  color: #222;
  pointer-events: none;
}

/* pointer-events: all when it's focused so that we can type in it */
.input:focus {
  border: none;
  outline: none;
  background: rgba(255, 255, 255, 0.25);
  pointer-events: all;
}
```

----------------------------------------

TITLE: Defining ReactFlowJsonObject Type in TypeScript
DESCRIPTION: This type definition outlines the structure of a `ReactFlowJsonObject`, which is used to represent the entire state of a React Flow instance in a JSON-compatible format. It includes arrays for `nodes` and `edges`, and an object for the `viewport` state, making it suitable for saving to and loading from databases or other storage.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/react-flow-json-object.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type ReactFlowJsonObject<T, U> = {
  nodes: Node<T>[];
  edges: Edge<U>[];
  viewport: Viewport;
};
```

----------------------------------------

TITLE: Defining Edge Marker Types with MarkerType Enum in TypeScript
DESCRIPTION: This TypeScript enum, `MarkerType`, defines the available options for markers on the ends of edges in the xyflow library. It provides string literal values 'arrow' and 'arrowclosed' which can be used to specify the visual style of an edge's start or end marker.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/marker-type.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export enum MarkerType {
  Arrow = 'arrow',
  ArrowClosed = 'arrowclosed',
}
```

----------------------------------------

TITLE: Defining NodeOrigin Type in TypeScript
DESCRIPTION: This type definition specifies the origin of a node as a tuple of two numbers, representing the x and y coordinates. These coordinates determine the placement of the node relative to its own position, with [0, 0] being the top-left and [1, 1] being the bottom-right.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/node-origin.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
export type NodeOrigin = [number, number];
```

----------------------------------------

TITLE: Adding BaseNode Component with shadcn CLI (Bash)
DESCRIPTION: This command uses the `shadcn` CLI to add the `<BaseNode />` component from the `ui.reactflow.dev` registry. It generates `src/components/base-node.tsx` and updates dependencies to include `@xyflow/react`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_11

LANGUAGE: bash
CODE:
```
npx shadcn@latest add https://ui.reactflow.dev/base-node
```

----------------------------------------

TITLE: Configuring Tailwind CSS Theme in JavaScript
DESCRIPTION: This snippet defines the `tailwind.config.js` file, specifying content paths for purging unused CSS, extending the default theme with custom border-radius values, and establishing a comprehensive color palette using CSS HSL variables for various UI elements like background, foreground, cards, popovers, and more.
SOURCE: https://github.com/xyflow/web/blob/main/apps/example-apps/react/tutorials/components/tooltip/index.html#_snippet_0

LANGUAGE: JavaScript
CODE:
```
/** @type {import('tailwindcss').Config} */
tailwind.config = {
  content: ['./index.html', './src/**/*.{'ts,tsx,js,jsx'}'],
  theme: {
    extend: {
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))'
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))'
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))'
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))'
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))'
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))'
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))'
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          1: 'hsl(var(--chart-1))',
          2: 'hsl(var(--chart-2))',
          3: 'hsl(var(--chart-3))',
          4: 'hsl(var(--chart-4))',
          5: 'hsl(var(--chart-5))'
        }
      }
    }
  }
};
```

----------------------------------------

TITLE: Updating Audio Node Data in JavaScript
DESCRIPTION: This function updates properties of an audio node. It iterates through the provided `data` object, checking if a property is an `AudioParam` to update its `value`, otherwise it directly assigns the new value. It requires the node's ID and a partial data object.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_20

LANGUAGE: javascript
CODE:
```
export function updateAudioNode(id, data) {
  const node = nodes.get(id);

  for (const [key, val] of Object.entries(data)) {
    if (node[key] instanceof AudioParam) {
      node[key].value = val;
    } else {
      node[key] = val;
    }
  }
}
```

----------------------------------------

TITLE: Checking for Edge object with isEdge() in Svelte
DESCRIPTION: This snippet demonstrates how to import and use the `isEdge` function from `@xyflow/svelte` to check if a given object conforms to the structure of an Edge. In a TypeScript context, this function acts as a type guard, narrowing the type of the object if it returns true.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/utils/is-edge.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { isEdge } from '@xyflow/svelte';

const edge = {
  id: 'edge-a',
  source: 'a',
  target: 'b',
};

if (isEdge(edge)) {
  // ..
}
```

----------------------------------------

TITLE: Defining Custom Edge Properties in xyflow React
DESCRIPTION: This TypeScript type definition outlines the `EdgeProps` interface, which specifies all the properties passed to a custom edge component in `xyflow` React. It includes essential attributes like `id`, `source`, `target`, `animated` status, styling options, and positional data, allowing for comprehensive customization of edge appearance and interaction. Developers use these props to render and manage custom edge logic within their React Flow applications.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/edge-props.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
export type EdgeProps<EdgeType extends Edge = Edge> = {
  id: string;
  animated: boolean;
  data: EdgeType['data'];
  style: React.CSSProperties;
  selected: boolean;
  source: string;
  target: string;
  sourceHandleId?: string | null;
  targetHandleId?: string | null;
  interactionWidth: number;
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
  sourcePosition: Position;
  targetPosition: Position;
  label?: string | React.ReactNode;
  labelStyle?: React.CSSProperties;
  labelShowBg?: boolean;
  labelBgStyle?: CSSProperties;
  labelBgPadding?: [number, number];
  labelBgBorderRadius?: number;
  markerStart?: string;
  markerEnd?: string;
  pathOptions?: any;
};
```

----------------------------------------

TITLE: Incorrectly Passing Node Types (JSX)
DESCRIPTION: This snippet shows an anti-pattern where `nodeTypes` are defined inline within the `ReactFlow` component. This practice creates a new object on every render, leading to performance issues and potential bugs due to unnecessary re-renders and component re-creations.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_4

LANGUAGE: jsx
CODE:
```
// this is bad! Don't do it.
<ReactFlow
  nodes={[]}
  nodeTypes={{
    specialType: SpecialNode, // bad!
  }}
/>
```

----------------------------------------

TITLE: Using useInternalNode Hook in Svelte
DESCRIPTION: This snippet demonstrates how to import and use the `useInternalNode` hook within a Svelte component script. It imports the hook from `@xyflow/svelte` and calls it, presumably passing a node `id` to retrieve the corresponding internal node object.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/hooks/use-internal-node.mdx#_snippet_0

LANGUAGE: Svelte
CODE:
```
<script lang="ts">
  import { useInternalNode } from '@xyflow/svelte';

  const node = useInternalNode(id);
</script>
```

----------------------------------------

TITLE: Installing React Flow Package (Bash)
DESCRIPTION: Install the official React Flow package from npm into your project's dependencies using the npm package manager.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/index.mdx#_snippet_2

LANGUAGE: Bash
CODE:
```
npm install @xyflow/react
```

----------------------------------------

TITLE: Defining Background Variants in TypeScript
DESCRIPTION: This enum defines the available visual variants for the background component in xyflow. Developers can use these variants by importing the enum (e.g., `BackgroundVariant.Lines`) or by directly using their string values. It provides options for 'lines', 'dots', and 'cross' patterns.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/background-variant.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export enum BackgroundVariant {
  Lines = 'lines',
  Dots = 'dots',
  Cross = 'cross',
}
```

----------------------------------------

TITLE: Accessing React Flow State with Provider Inside Component - JSX
DESCRIPTION: This example shows an incorrect usage where `useReactFlow` is called within the same component that renders `ReactFlowProvider`. The hook can only access the state if the component calling it is a child of the provider, not a sibling or parent. This setup will still lead to an error.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
import { ReactFlow, ReactFlowProvider } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

function Flow(props) {
  // still cannot access the state here
  // only child components of this component can access the state
  const reactFlowInstance = useReactFlow();

  return (
    <ReactFlowProvider>
      <ReactFlow {...props} />
    </ReactFlowProvider>
  );
}

export default FlowWithProvider;
```

----------------------------------------

TITLE: Defining XYPosition Type in TypeScript
DESCRIPTION: This TypeScript type defines a 2D position object, consisting of `x` and `y` properties, both of type `number`. It's used to represent coordinates for elements within the system.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/xy-position.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type XYPosition = {
  x: number;
  y: number;
};
```

----------------------------------------

TITLE: Scaffold React App with Vite (TypeScript)
DESCRIPTION: Command to create a new React project using Vite with the TypeScript template. Use this command if you prefer to build the app with TypeScript.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_1

LANGUAGE: bash
CODE:
```
npm create vite@latest reactflow-mind-map -- --template react-ts
```

----------------------------------------

TITLE: Styling React Flow Source Handle (Initial)
DESCRIPTION: Applies CSS styles to the default React Flow source handle to make it cover the entire node area, intended as an initial approach to using the node itself as a drag handle.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_19

LANGUAGE: css
CODE:
```
.react-flow__handle.source {
  top: 0;
  left: 0;
  transform: none;
  background: #f6ad55;
  height: 100%;
  width: 100%;
  border-radius: 2px;
  border: none;
}
```

----------------------------------------

TITLE: Style Target Handle in React Flow (CSS)
DESCRIPTION: CSS rule targeting the target handle (`.react-flow__handle.target`) in React Flow. It positions the handle vertically in the center, disables pointer events, and sets opacity to 0, effectively hiding the target handle to simplify the user experience for creating new nodes by dropping edges on the pane.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_18

LANGUAGE: css
CODE:
```
.react-flow__handle.target {
  top: 50%;
  pointer-events: none;
  opacity: 0;
}
```

----------------------------------------

TITLE: Installing Node.js Types for Vite Path Resolution (Bash)
DESCRIPTION: This command installs the `@types/node` development dependency. This package provides TypeScript type definitions for Node.js, which is necessary for Vite to correctly resolve path aliases, particularly when using `node:path` in `vite.config.js`. It's a prerequisite for the subsequent Vite configuration.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_7

LANGUAGE: bash
CODE:
```
npm i -D @types/node
```

----------------------------------------

TITLE: Defining PanelPosition Type in TypeScript
DESCRIPTION: Defines the `PanelPosition` type as a union of string literals, representing the six possible positions for UI panels within the xyflow viewport (top, bottom, left, center, right combinations). This type is used for positioning components like MiniMap and Controls.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/panel-position.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type PanelPosition =
  | 'top-left'
  | 'top-center'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-center'
  | 'bottom-right';
```

----------------------------------------

TITLE: Scaffolding New Examples with pnpm
DESCRIPTION: This snippet details the usage of the `pnpm scaffold` command, a script designed to quickly generate boilerplate for new React Flow or Svelte Flow examples. It requires specifying the target framework ('react' or 'svelte') and the desired route fragment, which determines the example's file placement and URL.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/whats-new/2024-10-16.mdx#_snippet_0

LANGUAGE: Markdown
CODE:
```
The scaffold script helps you quickly put together a new example for either\nreactflow.dev or svelteflow.dev by copying over the boilerplate. All arguments\nare _required_.\n\nUSAGE:\n\npnpm scaffold <FRAMEWORK> <ROUTE>\n\nEXAMPLES:\n\npnpm scaffold react blog/web-audio/demo\n\npnpm scaffold svelte guides/getting-started\n\nARGUMENTS:\n\nFRAMEWORK 'react' | 'svelte'\n\n              The framework the example will be written in. This affects where\n              the generated files are placed in conjunction with the ROUTE\n              argument.\n\nROUTE string\n\n              The route fragment the example app will be served at when combined\n              with the FRAMEWORK argument. For example, calling the script as\n              `pnpm scaffold react examples/nodes/custom-node` will scaffold\n              the example and make it accessible at\n              '/react/examples/nodes/custom-node/index.html'.
```

----------------------------------------

TITLE: Defining React Flow Store with Zustand - TypeScript
DESCRIPTION: Sets up a Zustand store (`useStore`) to manage the state of React Flow nodes and edges. It includes initial state definition and handlers (`onNodesChange`, `onEdgesChange`) that apply changes using React Flow's utility functions. Dependencies include `@xyflow/react` and `zustand/traditional`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_6

LANGUAGE: typescript
CODE:
```
import {
  Edge,
  EdgeChange,
  Node,
  NodeChange,
  OnNodesChange,
  OnEdgesChange,
  applyNodeChanges,
  applyEdgeChanges,
} from '@xyflow/react';
import { createWithEqualityFn } from 'zustand/traditional';

export type RFState = {
  nodes: Node[];
  edges: Edge[];
  onNodesChange: OnNodesChange;
  onEdgesChange: OnEdgesChange;
};

const useStore = createWithEqualityFn<RFState>((set, get) => ({
  nodes: [
    {
      id: 'root',
      type: 'mindmap',
      data: { label: 'React Flow Mind Map' },
      position: { x: 0, y: 0 },
    },
  ],
  edges: [],
  onNodesChange: (changes: NodeChange[]) => {
    set({
      nodes: applyNodeChanges(changes, get().nodes),
    });
  },
  onEdgesChange: (changes: EdgeChange[]) => {
    set({
      edges: applyEdgeChanges(changes, get().edges),
    });
  },
}));

export default useStore;
```

----------------------------------------

TITLE: Configuring FitView Padding with Mixed Units - React Flow - TypeScript
DESCRIPTION: This example illustrates the enhanced `fitViewOptions` padding configuration, allowing developers to specify padding using various units like pixels ('px'), viewport percentages ('%'), or legacy numeric values. It supports individual padding for horizontal (x), vertical (y), and specific sides (top, left, bottom, right), enabling precise control over the fitted view area.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/whats-new/2025-03-27.mdx#_snippet_2

LANGUAGE: TypeScript
CODE:
```
const fitViewOptions = {
  padding: {
    /** horizontal */
    x: '100px',
    /** vertical */
    y: '50px',
    /** e.g. top overwrites x */
    top: '25px',
    /** mix and match units */
    left: '15%',
    /** legacy units still work */
    bottom: 0.1,
    /** have a modal on the right that stretches 50% over the screen? */
    right: '50%',
  },
};
```

----------------------------------------

TITLE: Customizing BaseNode Component Style (JSX)
DESCRIPTION: This JSX code modifies the `src/components/base-node.tsx` file to change the styling of the `<BaseNode />` component. It updates the `className` to render text as bold monospace, demonstrating how to customize the base component's appearance.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_13

LANGUAGE: jsx
CODE:
```
import React from "react";
import { cn } from "@/lib/utils";

export const BaseNode = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { selected?: boolean }
>(({ className, selected, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-md border bg-card p-5 text-card-foreground font-mono font-bold",
      className,
      selected ? "border-muted-foreground shadow-lg" : "",
      "hover:ring-1"
    )}
    {...props}
  />
));
BaseNode.displayName = "BaseNode";
```

----------------------------------------

TITLE: Configuring Main Application Entry Point with React Flow Provider
DESCRIPTION: This `main.tsx` file configures the root of the React application. It imports `ReactFlowProvider` and React Flow's default styles, wrapping the main `App` component within `ReactFlowProvider` to ensure access to the React Flow instance throughout the application. It also sets a default width and height for the React Flow canvas.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
import React from 'react';
import ReactDOM from 'react-dom/client';
import { ReactFlowProvider } from '@xyflow/react';

import App from './App';

import '@xyflow/react/dist/style.css';
import './index.css';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ReactFlowProvider>
      {/* The parent element of the React Flow component needs a width and a height
          to work properly. If you're styling your app as you follow along, you
          can remove this div and apply styles to the #root element in your CSS.
       */}
      <div style={{ width: '100vw', height: '100vh' }}>
        <App />
      </div>
    </ReactFlowProvider>
  </React.StrictMode>,
);
```

----------------------------------------

TITLE: Define State with Writable in Svelte Flow v0 (Old API)
DESCRIPTION: Shows the previous method for defining `nodes` and `edges` state using Svelte's `writable` store, common in Svelte Flow 0.x applications.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
const nodes = writable([...]);
const edges = writable([...]);
```

----------------------------------------

TITLE: Configuring Webpack 4 Babel Loader for React Flow
DESCRIPTION: This Webpack 4 configuration snippet defines a rule to process React Flow's JavaScript files using `babel-loader`. It applies `@babel/preset-env` and `@babel/preset-react` along with `@babel/plugin-proposal-optional-chaining` and `@babel/plugin-proposal-nullish-coalescing-operator` to transpile modern JavaScript syntax, resolving 'Module parse failed' errors when building React Flow applications with Webpack 4.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_16

LANGUAGE: js
CODE:
```
{
  test: /node_modules[\/\\]@?reactflow[\/\\].*\.js$/,
  use: {
    loader: 'babel-loader',
    options: {
      presets: ['@babel/preset-env', "@babel/preset-react"],
      plugins: [
        "@babel/plugin-proposal-optional-chaining",
        "@babel/plugin-proposal-nullish-coalescing-operator"
      ]
    }
  }
}
```

----------------------------------------

TITLE: Configuring Vite to Resolve TypeScript Path Aliases (JavaScript)
DESCRIPTION: This `vite.config.js` snippet configures Vite to resolve the `@` path alias defined in `tsconfig.json`. It imports `path` from `node:path` and uses `path.resolve` to map `@` to the `./src` directory. This ensures that imports using the `@/` prefix are correctly resolved by Vite during development and build processes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_8

LANGUAGE: javascript
CODE:
```
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'node:path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});
```

----------------------------------------

TITLE: Configuring Tailwind CSS Theme in JavaScript
DESCRIPTION: This snippet defines the `tailwind.config.js` object, specifying content files for purging unused CSS, and extending the default theme with custom border-radius values and a comprehensive color palette. It uses CSS variables (e.g., `var(--background)`) for dynamic theming, providing a flexible and maintainable design system.
SOURCE: https://github.com/xyflow/web/blob/main/apps/example-apps/react/tutorials/components/sum-node/index.html#_snippet_0

LANGUAGE: JavaScript
CODE:
```
/** @type {import('tailwindcss').Config} */ tailwind.config = { content: ['./index.html', './src/**/*.{ts,tsx,js,jsx}'], theme: { extend: { borderRadius: { lg: 'var(--radius)', md: 'calc(var(--radius) - 2px)', sm: 'calc(var(--radius) - 4px)', }, colors: { background: 'hsl(var(--background))', foreground: 'hsl(var(--foreground))', card: { DEFAULT: 'hsl(var(--card))', foreground: 'hsl(var(--card-foreground))', }, popover: { DEFAULT: 'hsl(var(--popover))', foreground: 'hsl(var(--popover-foreground))', }, primary: { DEFAULT: 'hsl(var(--primary))', foreground: 'hsl(var(--primary-foreground))', }, secondary: { DEFAULT: 'hsl(var(--secondary))', foreground: 'hsl(var(--secondary-foreground))', }, muted: { DEFAULT: 'hsl(var(--muted))', foreground: 'hsl(var(--muted-foreground))', }, accent: { DEFAULT: 'hsl(var(--accent))', foreground: 'hsl(var(--accent-foreground))', }, destructive: { DEFAULT: 'hsl(var(--destructive))', foreground: 'hsl(var(--destructive-foreground))', }, border: 'hsl(var(--border))', input: 'hsl(var(--input))', ring: 'hsl(var(--ring))', chart: { 1: 'hsl(var(--chart-1))', 2: 'hsl(var(--chart-2))', 3: 'hsl(var(--chart-3))', 4: 'hsl(var(--chart-4))', 5: 'hsl(var(--chart-5))', }, }, }, }, };
```

----------------------------------------

TITLE: Implementing Node Connection Start/End Handlers (React/TypeScript)
DESCRIPTION: Implements the `onConnectStart` and `onConnectEnd` event handlers provided by React Flow. The `onConnectStart` handler captures and stores the ID of the node where a connection drag originates using a `useRef`. The `onConnectEnd` handler checks if the drag ends on the React Flow pane, indicating an intent to create a new node, and logs the parent node ID as a preliminary step.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_12

LANGUAGE: tsx
CODE:
```
const connectingNodeId = useRef<string | null>(null);

const onConnectStart: OnConnectStart = useCallback((_, { nodeId }) => {
  connectingNodeId.current = nodeId;
}, []);

const onConnectEnd: OnConnectEnd = useCallback((event) => {
  // we only want to create a new node if the connection ends on the pane
  const targetIsPane = (event.target as Element).classList.contains('react-flow__pane');

  if (targetIsPane && connectingNodeId.current) {
    console.log(`add new node with parent node ${connectingNodeId.current}`);
  }
}, []);
```

----------------------------------------

TITLE: Basic Usage of useNodesState and useEdgesState with ReactFlow (JSX)
DESCRIPTION: This snippet demonstrates the fundamental usage of `useNodesState` and `useEdgesState` hooks to manage the state of nodes and edges in a React Flow application. It shows how to initialize the state and pass the `nodes`, `edges`, `onNodesChange`, and `onEdgesChange` props to the `ReactFlow` component for a controlled flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-nodes-state.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { ReactFlow, useNodesState, useEdgesState } from '@xyflow/react';

const initialNodes = [];
const initialEdges = [];

export default function () {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  return (
    <ReactFlow
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
    />
  );
}
```

----------------------------------------

TITLE: Defining Default Edge Options Type in React Flow (TypeScript)
DESCRIPTION: This TypeScript type definition outlines the optional properties that can be set as default values for new edges in a React Flow application. These options are used by the `<ReactFlow />` component to automatically populate properties like type, animation status, visibility, data, and markers when an edge is created without explicitly defining them.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/default-edge-options.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type DefaultEdgeOptions<T> = {
  type?: string;
  animated?: boolean;
  hidden?: boolean;
  deletable?: boolean;
  selectable?: boolean;
  data?: T;
  selected?: boolean;
  markerStart?: string | EdgeMarker;
  markerEnd?: string | EdgeMarker;
  zIndex?: number;
  ariaLabel?: string;
  interactionWidth?: number;
  focusable?: boolean;
};
```

----------------------------------------

TITLE: Getting Viewport Transform from Bounds (Deprecated) - JavaScript
DESCRIPTION: This snippet illustrates the deprecated `getTransformForBounds` utility. It returned the viewport transformation as a `[x, y, zoom]` array, which could be less clear regarding the meaning of each element. This function is being replaced for a more explicit return type.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/whats-new/2023-11-10.mdx#_snippet_2

LANGUAGE: JavaScript
CODE:
```
const [x, y, zoom] = getTransformForBounds(bounds, width, height, 0.5, 2);
```

----------------------------------------

TITLE: Straight SVG Path String Example
DESCRIPTION: This snippet illustrates a simple straight SVG path string. The 'M' command moves the drawing pen to the starting coordinates (x1, y1) without drawing, and the 'L' command then draws a line from that point to the ending coordinates (x2, y2). This is a fundamental example of how SVG path commands are concatenated to define a shape.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/custom-edges.mdx#_snippet_4

LANGUAGE: jsx
CODE:
```
M x1 y1 L x2 y2
```

----------------------------------------

TITLE: Defining DeleteElements Type in TypeScript
DESCRIPTION: This TypeScript type definition outlines the `DeleteElements` function signature. It accepts an object containing optional arrays of partial `Node` and `Edge` objects (identified by their `id`) to be deleted. The function returns a Promise that resolves to an object containing arrays of the `Node` and `Edge` objects that were successfully deleted.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/delete-elements.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type DeleteElements = (payload: {
  nodes?: (Partial<Node> & { id: Node['id'] })[];
  edges?: (Partial<Edge> & { id: Edge['id'] })[];
}) => Promise<{
  deletedNodes: Node[];
  deletedEdges: Edge[];
}>;
```

----------------------------------------

TITLE: Applying Parent Extent to a Non-Child Node in React Flow (Incorrect)
DESCRIPTION: This code demonstrates an incorrect usage of the `extent: 'parent'` option. This warning occurs when `extent: 'parent'` is applied to a node that does not have a `parentNode` defined, as the extent property is only valid for child nodes within a parent's bounds.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_11

LANGUAGE: jsx
CODE:
```
import { ReactFlow } from '@xyflow/react';
import '@xyflow/react/dist/style.2.css';

const nodes = [
  {
    id: 'mycustomnode',
    extent: 'parent',
    // ...
  },
];

function Flow(props) {
  return <ReactFlow nodes={nodes} />;
}
```

----------------------------------------

TITLE: Integrating BaseNode into React App (JSX)
DESCRIPTION: This JSX code updates `App.jsx` to import and render the newly added `<BaseNode />` component. It demonstrates how to use the component within a basic React application structure, showing its initial appearance.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_12

LANGUAGE: jsx
CODE:
```
import '@xyflow/react/dist/style.css';

import { BaseNode } from '@/components/base-node';

function App() {
  return (
    <div className="w-screen h-screen p-8">
      <BaseNode selected={false}>Hi! 👋</BaseNode>
    </div>
  );
}

export default App;
```

----------------------------------------

TITLE: Configuring Tailwind CSS Content Paths (JavaScript)
DESCRIPTION: This JavaScript snippet updates the `tailwind.config.js` file to specify which files Tailwind CSS should scan for utility classes. This ensures that only the necessary CSS is generated, optimizing the final bundle size.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_3

LANGUAGE: javascript
CODE:
```
/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{ts,tsx,js,jsx}'],
  theme: {
    extend: {},
  },
  plugins: []
};
```

----------------------------------------

TITLE: Installing Svelte Flow 1.0 Alpha
DESCRIPTION: This command installs the alpha version of Svelte Flow 1.0, which is built on Svelte 5. It allows users to try out the latest developments and changes in the Svelte Flow library, enabling early access to new features and improvements.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/src/content/spring-update-2025.mdx#_snippet_0

LANGUAGE: Shell
CODE:
```
npm install @xyflow/svelte
```

----------------------------------------

TITLE: Adding Components with shadcn/cli (Bash)
DESCRIPTION: This snippet demonstrates how to initialize shadcn and add a new React Flow component using the shadcn CLI. It involves running `npx shadcn init` for initial setup and `npx shadcn add` to fetch and integrate a component from a specified URL.
SOURCE: https://github.com/xyflow/web/blob/main/apps/ui-components/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
npx shadcn init # initialize shadcn
npx shadcn add http://ui.reactflow.dev/<component-name>.json ## adding component
```

----------------------------------------

TITLE: Initializing shadcn-ui with CLI (Bash)
DESCRIPTION: This command initializes the shadcn-ui configuration in your project. It sets up the necessary files and dependencies for shadcn-ui, allowing you to use its components. This is a prerequisite step before adding specific React Flow components.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/components/index.mdx#_snippet_0

LANGUAGE: bash
CODE:
```
npx shadcn@latest init
```

----------------------------------------

TITLE: Configuring TypeScript Paths for shadcn/ui (JSON)
DESCRIPTION: This snippet shows the necessary `compilerOptions` to add to `tsconfig.json` and `tsconfig.app.json`. It configures `baseUrl` to the current directory and sets up a path alias `@/*` to resolve to `./src/*`, allowing shadcn components to correctly resolve their internal imports. This is a prerequisite for shadcn/ui to function properly.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_6

LANGUAGE: json
CODE:
```
{
  ...
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

----------------------------------------

TITLE: Generating Tailwind CSS Configuration (Bash)
DESCRIPTION: This command initializes a `tailwind.config.js` file and a `postcss.config.js` file (due to `-p` flag). These configuration files are essential for customizing Tailwind CSS and integrating it with PostCSS.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_2

LANGUAGE: bash
CODE:
```
npx tailwindcss init -p
```

----------------------------------------

TITLE: Adding TooltipNode Component with shadcn CLI (Bash)
DESCRIPTION: This command uses the `shadcn` CLI to add the `<TooltipNode />` component from the `ui.reactflow.dev` registry. This component is expected to automatically inherit the styles defined in the customized `<BaseNode />`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_14

LANGUAGE: bash
CODE:
```
npx shadcn@latest add https://ui.reactflow.dev/tooltip-node
```

----------------------------------------

TITLE: Setting Up Web Audio Context and Nodes
DESCRIPTION: This JavaScript snippet initializes the core components of a Web Audio graph: an AudioContext, an OscillatorNode for tone generation, and a GainNode for volume control. It connects the oscillator's output through the gain node and then to the audio destination, finally starting the oscillator to begin tone generation.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_1

LANGUAGE: javascript
CODE:
```
// Create the brain of our audio-processing graph
const context = new AudioContext();

// Create an oscillator node to generate tones
const osc = context.createOscillator();

// Create a gain node to control the volume
const amp = context.createGain();

// Pass the oscillator's output through the gain node and to our speakers
osc.connect(amp);
amp.connect(context.destination);

// Start generating those tones!
osc.start();
```

----------------------------------------

TITLE: Registering Custom Node and Edge Types (React/TypeScript)
DESCRIPTION: Illustrates the process of registering custom node and edge components (`MindMapNode` and `MindMapEdge`) for integration with the React Flow component. It involves creating `nodeTypes` and `edgeTypes` objects, mapping the string identifier 'mindmap' to the respective component implementations. These objects are then passed to the main `ReactFlow` component to enable the use of the custom types.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_11

LANGUAGE: tsx
CODE:
```
import MindMapNode from './MindMapNode';
import MindMapEdge from './MindMapEdge';

const nodeTypes = {
  mindmap: MindMapNode,
};

const edgeTypes = {
  mindmap: MindMapEdge,
};
```

----------------------------------------

TITLE: Specifying Svelte Flow Node Handle Positions for SSR (Svelte)
DESCRIPTION: Illustrates how to define the `handles` array for a node, specifying the type, position (using `Position` enum), and exact `x` and `y` coordinates for each handle. This is required for rendering edges correctly on the server.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/advanced/server-side-rendering.mdx#_snippet_1

LANGUAGE: svelte
CODE:
```
<script>
  import { Position } from '@xyflow/svelte';

  const nodes = [
    {
      id: '1',
      type: 'default',
      position: { x: 0, y: 0 },
      data: { label: 'Node 1' },
      width: 100,
      height: 50,
      handles: [
        {
          type: 'target',
          position: Position.Top,
          x: 100 / 2,
          y: 0,
        },
        {
          type: 'source',
          position: Position.Bottom,
          x: 100 / 2,
          y: 50,
        },
      ],
    },
  ];
</script>
```

----------------------------------------

TITLE: Using useInternalNode to Get Node Position in React
DESCRIPTION: This snippet demonstrates how to use the `useInternalNode` hook from `@xyflow/react` to retrieve the internal representation of a specific node by its ID. It then accesses the `positionAbsolute` property to display the node's absolute X and Y coordinates within a React component. Components using this hook will re-render on any node changes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-internal-node.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useInternalNode } from '@xyflow/react';

export default function () {
  const internalNode = useInternalNode('node-1');
  const absolutePosition = internalNode.internals.positionAbsolute;

  return (
    <div>
      The absolute position of the node is at:
      <p>x: {absolutePosition.x}</p>
      <p>y: {absolutePosition.y}</p>
    </div>
  );
}
```

----------------------------------------

TITLE: Configuring Tailwind CSS Theme and Content Paths in JavaScript
DESCRIPTION: This snippet defines the Tailwind CSS configuration object. It specifies the files to scan for Tailwind classes (`content`) and extends the default theme. The `extend` property customizes `borderRadius` with CSS variables and defines a comprehensive set of semantic colors (e.g., `background`, `primary`, `destructive`, `chart`) using HSL values derived from CSS variables, enabling dynamic theming.
SOURCE: https://github.com/xyflow/web/blob/main/apps/example-apps/react/tutorials/components/complete/index.html#_snippet_0

LANGUAGE: JavaScript
CODE:
```
/** @type {import('tailwindcss').Config} */
tailwind.config = {
  content: ['./index.html', './src/**/*.{ts,tsx,js,jsx}'],
  theme: {
    extend: {
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          1: 'hsl(var(--chart-1))',
          2: 'hsl(var(--chart-2))',
          3: 'hsl(var(--chart-3))',
          4: 'hsl(var(--chart-4))',
          5: 'hsl(var(--chart-5))',
        }
      }
    }
  }
};
```

----------------------------------------

TITLE: Defining PanOnScrollMode Enum in TypeScript
DESCRIPTION: This enum defines the available modes for panning the viewport when a user scrolls. 'Free' allows unrestricted panning, 'Vertical' restricts panning to the vertical axis, and 'Horizontal' restricts it to the horizontal axis.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/pan-on-scroll-mode.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export enum PanOnScrollMode {
  Free = 'free',
  Vertical = 'vertical',
  Horizontal = 'horizontal',
}
```

----------------------------------------

TITLE: Defining BackgroundVariant Enum in TypeScript
DESCRIPTION: This TypeScript enum defines the available variants for a background component, providing symbolic names (Lines, Dots, Cross) for their corresponding string values ('lines', 'dots', 'cross'). Users can import and use the enum members or use the raw string values directly.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/background-variant.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export enum BackgroundVariant {
  Lines = 'lines',
  Dots = 'dots',
  Cross = 'cross',
}
```

----------------------------------------

TITLE: Defining Initial Nodes in Zustand Store (JavaScript)
DESCRIPTION: This JavaScript snippet from `./src/store.js` demonstrates how to define initial nodes within a Zustand store. It sets up an 'osc' type node with a specific ID, data (frequency, type), and position, serving as a starting point for the React Flow graph.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_15

LANGUAGE: JavaScript
CODE:
```
const useStore = createWithEqualityFn((set, get) => ({
  nodes: [
    { type: 'osc',
      id: 'a',
      data: { frequency: 220, type: 'square' },
      position: { x: 0, y: 0 }
    },
    ...
  ],
  ...
}));
```

----------------------------------------

TITLE: Adding React Flow Components with shadcn CLI (Bash)
DESCRIPTION: This command adds a specific React Flow component from the ui.reactflow.dev repository to your project. It copies the component's code into your designated components folder, automatically installs its dependencies, and handles potential overwrites of existing components.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/components/index.mdx#_snippet_1

LANGUAGE: bash
CODE:
```
npx shadcn@latest add https://ui.reactflow.dev/component-name
```

----------------------------------------

TITLE: Defining the HandleConnection Type in TypeScript
DESCRIPTION: This TypeScript type definition extends the basic `Connection` type by adding an `edgeId` property. It specifies the `source`, `target`, `sourceHandle`, and `targetHandle` as strings or null, and `edgeId` as a required string, used for identifying the edge associated with the connection.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/handle-connection.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type HandleConnection = {
  source: string | null;
  target: string | null;
  sourceHandle: string | null;
  targetHandle: string | null;
  edgeId: string;
};
```

----------------------------------------

TITLE: Set Fullscreen Styles (CSS)
DESCRIPTION: CSS rules applied to `body`, `html`, and the root element (`#root`) to ensure the application container takes up the full height of the viewport. This is necessary for the React Flow component to render correctly in a fullscreen layout.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_5

LANGUAGE: css
CODE:
```
body {
  margin: 0;
}

html,
body,
#root {
  height: 100%;
}
```

----------------------------------------

TITLE: Initializing shadcn/ui Project Configuration (Bash)
DESCRIPTION: This command initializes the shadcn/ui setup in the current project. It runs the latest version of the shadcn CLI, which will prompt the user for configuration preferences (e.g., style, color, CSS variables) and then generate a `components.json` file and update `tailwind.config.js` with theme extensions. This command is the final step in the initial setup.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_9

LANGUAGE: bash
CODE:
```
npx shadcn@latest init
```

----------------------------------------

TITLE: Defining FitViewOptions Type (TypeScript)
DESCRIPTION: This snippet defines the `FitViewOptions` type, which specifies the configuration for the `fitView` function in `@xyflow/react`. It highlights that the `nodes` property, when provided, only requires an `id` for each node, allowing for flexible usage without needing full node objects.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/slide-shows-with-react-flow.mdx#_snippet_12

LANGUAGE: typescript
CODE:
```
export type FitViewOptions = {
  padding?: number;
  includeHiddenNodes?: boolean;
  minZoom?: number;
  maxZoom?: number;
  duration?: number;
  nodes?: (Partial<Node> & { id: Node['id'] })[];
};
```

----------------------------------------

TITLE: Configuring Tailwind CSS Content Paths for @xyflow/xy-ui (JavaScript)
DESCRIPTION: This snippet demonstrates how to extend the `content` array in `tailwind.config.js` to include the paths for `@xyflow/xy-ui` components. By doing so, Tailwind CSS will scan these files for class names, ensuring that all necessary utility classes are generated and included in the final CSS bundle for the UI components.
SOURCE: https://github.com/xyflow/web/blob/main/packages/xy-ui/README.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
content: {
  ...
  'node_modules/@xyflow/xy-ui/components/**/*.{js,ts,jsx,tsx,mdx}',
}
```

----------------------------------------

TITLE: Calculating Straight Path with xyflow/svelte JavaScript
DESCRIPTION: Demonstrates how to use the getStraightPath function from @xyflow/svelte to calculate a straight line path between two points. It takes source and target coordinates and returns the SVG path string along with label and offset coordinates.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/utils/get-straight-path.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { getStraightPath } from '@xyflow/svelte';

const source = { x: 0, y: 20 };
const target = { x: 150, y: 100 };

const [path, labelX, labelY, offsetX, offsetY] = getStraightPath({
  sourceX: source.x,
  sourceY: source.y,
  targetX: target.x,
  targetY: target.y,
});

console.log(path); //=> "M 0,20L 150,100"
console.log(labelX, labelY); //=> 75, 60
console.log(offsetX, offsetY); //=> 75, 40
```

----------------------------------------

TITLE: Connecting Node Deletion to React Flow in JSX
DESCRIPTION: This snippet configures the `App` component to handle node deletions. It uses a selector to map the `store.removeNodes` action to React Flow's `onNodesDelete` prop, allowing the UI to trigger the store's node removal logic when nodes are deleted from the graph.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_24

LANGUAGE: jsx
CODE:
```
const selector = store => ({
  ...,
  onNodesDelete: store.removeNodes
});

export default function App() {
  const store = useStore(selector, shallow);

  return (
    <ReactFlow
      onNodesDelete={store.onNodesDelete}
      ...
    >
      <Background />
    </ReactFlow>
  )
};
```

----------------------------------------

TITLE: Using OnEdgesChange with useCallback in React Flow
DESCRIPTION: This snippet demonstrates the practical usage of the `OnEdgesChange` type within a React component. It defines an `onEdgesChange` handler using the `useCallback` hook to optimize performance, applying incoming `changes` to the existing edges state via the `applyEdgeChanges` utility function. This function is typically passed to the `onEdgesChange` prop of the React Flow component.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/on-edges-change.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
const onEdgesChange: OnEdgesChange = useCallback(
  (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
  [setEdges],
);
```

----------------------------------------

TITLE: Defining ColorMode Type in TypeScript
DESCRIPTION: This TypeScript type definition specifies the possible values for `ColorMode`, allowing it to be 'light', 'dark', or 'system'. This type is typically used to control the visual theme or appearance settings within an application.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/color-mode.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type ColorMode = 'light' | 'dark' | 'system';
```

----------------------------------------

TITLE: Integrating Node Removal with Store in JavaScript
DESCRIPTION: This `removeNodes` action in the Zustand store iterates through an array of nodes to be removed, calling `removeAudioNode` for each one. This ensures that audio-specific cleanup is performed before the nodes are removed from the React Flow state.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_23

LANGUAGE: javascript
CODE:
```
import { ..., removeAudioNode } from './audio';

export const useStore = createWithEqualityFn((set, get) => ({
  ...

  removeNodes(nodes) {
    for (const { id } of nodes) {
      removeAudioNode(id)
    }
  },

  ...
}));
```

----------------------------------------

TITLE: Displaying Image in React Flow Blog Post
DESCRIPTION: This JSX snippet demonstrates how an `Image` component is used within a blog post to display a screenshot. It specifies the image source, alternative text for accessibility, dimensions, and attribution link. This is common in Docusaurus-based blogs.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/src/content/spring-update-2025.mdx#_snippet_1

LANGUAGE: JSX
CODE:
```
<Image
  src="/img/blog/2025-spring/simpleai.png"
  alt="A Screenshot of the simpleai parallelization workflow."
  width={1400}
  height={783}
  attribution="https://www.simple-ai.dev/ai-agents"
/>
```

----------------------------------------

TITLE: Installing Babel Dependencies for Webpack 4 with React Flow
DESCRIPTION: This command installs the necessary Babel dependencies, including `babel-loader`, `preset-env`, `preset-react`, and two proposal plugins, required to transpile modern JavaScript features used by React Flow when building with Webpack 4. These packages enable Webpack 4 to correctly process React Flow's ES module code.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/index.mdx#_snippet_15

LANGUAGE: sh
CODE:
```
npm i --save-dev babel-loader@8.2.5 @babel/preset-env @babel/preset-react @babel/plugin-proposal-optional-chaining @babel/plugin-proposal-nullish-coalescing-operator
```

----------------------------------------

TITLE: Getting Incomer Nodes with getIncomers in TypeScript
DESCRIPTION: This snippet demonstrates how to use the `getIncomers` utility from `@xyflow/react` to find all nodes that are connected as the source of an edge to a specified target node. It requires a target node object, an array of all nodes, and an array of all edges as inputs.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/utils/get-incomers.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { getIncomers } from '@xyflow/react';

const nodes = [];
const edges = [];

const incomers = getIncomers(
  { id: '1', position: { x: 0, y: 0 }, data: { label: 'node' } },
  nodes,
  edges,
);
```

----------------------------------------

TITLE: Implementing onConnect Handler with addEdge in React Flow
DESCRIPTION: This example shows how to implement the `onConnect` handler to manage new connections between nodes. It utilizes the `addEdge` helper function to append the newly created edge to the existing array of edges, optionally applying custom options like `animated: true`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/concepts/core-concepts.mdx#_snippet_1

LANGUAGE: JavaScript
CODE:
```
const onConnect = useCallback(
  (connection) =>
    setEdges((eds) => addEdge({ ...connection, animated: true }, eds)),
  [setEdges],
);
```

----------------------------------------

TITLE: Using useNodesData with Custom Node Types in TypeScript
DESCRIPTION: This snippet illustrates how to apply a generic type argument (`NodesType`) to the `useNodesData` hook when working with custom node types in TypeScript. This allows for type-safe access to node data, ensuring that the retrieved data conforms to the expected structure defined by the `NodesType` union.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-nodes-data.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
const nodesData = useNodesData<NodesType>(['nodeId-1', 'nodeId-2']);
```

----------------------------------------

TITLE: Defining ProOptions Type in TypeScript
DESCRIPTION: This TypeScript type definition outlines the available options for Pro features within xyflow. The `hideAttribution` property is an optional boolean that, when set to `true`, enables the removal of the default attribution link displayed in the corner of the flows, offering customization for the user interface.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/pro-options.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
type ProOptions = {
  hideAttribution?: boolean;
};
```

----------------------------------------

TITLE: Defining Edge with Custom Markers (New API) - JavaScript
DESCRIPTION: This snippet demonstrates the new `markerStart` and `markerEnd` properties for edges, allowing individual customization of markers at both ends. Markers can be a string ID for custom SVG or a configuration object for built-in types with color and stroke width.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_19

LANGUAGE: js
CODE:
```
const markerEdge = {
  source: '1',
  target: '2',
  markerStart: 'myCustomSvgMarker',
  markerEnd: { type: 'arrow', color: '#f00' },
};
```

----------------------------------------

TITLE: Create Base React Flow Component (TSX)
DESCRIPTION: Defines the main `Flow` component using React Flow. It imports necessary components (`ReactFlow`, `Controls`, `Panel`) and the default React Flow styles. It renders a basic React Flow container with controls and a title panel.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
import { ReactFlow, Controls, Panel } from '@xyflow/react';

// we have to import the React Flow styles for it to work
import '@xyflow/react/dist/style.css';

function Flow() {
  return (
    <ReactFlow>
      <Controls showInteractive={false} />
      <Panel position="top-left">React Flow Mind Map</Panel>
    </ReactFlow>
  );
}

export default Flow;
```

----------------------------------------

TITLE: Wrap App with ReactFlowProvider (TSX)
DESCRIPTION: The entry file (`main.tsx`) for the Vite application. It wraps the main `App` component with `ReactFlowProvider` to enable the use of React Flow hooks throughout the component tree. It also imports the global CSS styles.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
import React from 'react';
import ReactDOM from 'react-dom/client';
import { ReactFlowProvider } from '@xyflow/react';

import App from './App';

import './index.css';

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <ReactFlowProvider>
      <App />
    </ReactFlowProvider>
  </React.StrictMode>,
);
```

----------------------------------------

TITLE: Importing React Flow v11 Styles
DESCRIPTION: This snippet shows how to import the necessary CSS styles for React Flow v11. Unlike previous versions, CSS is no longer injected automatically and must be explicitly imported for the library to function correctly. You can choose between the full `style.css` or the basic `base.css`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v11.mdx#_snippet_2

LANGUAGE: javascript
CODE:
```
// default styling
import '@xyflow/react/dist/style.css';

// or if you just want basic styles
import '@xyflow/react/dist/base.css';
```

----------------------------------------

TITLE: Defining NodeConnection Type in TypeScript
DESCRIPTION: This snippet defines the `NodeConnection` type, which extends a basic `Connection` by adding an `edgeId`. It specifies properties for source, target, sourceHandle, targetHandle, and the unique edge identifier. This type is crucial for representing connections between nodes within the xyflow system.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/node-connection.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
export type NodeConnection = {
  source: string | null;
  target: string | null;
  sourceHandle: string | null;
  targetHandle: string | null;
  edgeId: string;
};
```

----------------------------------------

TITLE: Rendering Image Component - JSX
DESCRIPTION: This snippet demonstrates how to use the imported `Image` component to display an image within a React application. It specifies the image source via the `src` prop and provides an `alt` text for accessibility, which is crucial for screen readers and SEO.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/whats-new/2023-12-06.mdx#_snippet_1

LANGUAGE: JSX
CODE:
```
<Image\n  src="/img/whats-new/pro-platform-open-source.png"\n  alt="A screenshot of the new Pro Platform design."\n/>
```

----------------------------------------

TITLE: Specifying Custom Node Types with useNodesState (TypeScript)
DESCRIPTION: This example illustrates how to use a generic type argument with the `useNodesState` hook in TypeScript. By specifying `<CustomNodeType>`, developers can define and enforce the structure of their custom node types, enhancing type safety and improving code maintainability within a React Flow application.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-nodes-state.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
const nodes = useNodesState<CustomNodeType>();
```

----------------------------------------

TITLE: Defining a Node Object with Custom Type (JavaScript)
DESCRIPTION: This JavaScript code demonstrates how to define a node object that uses a custom node type registered with Svelte Flow. The `type` property of the node object is set to the string key ('textUpdater') that was used when registering the custom component in the `nodeTypes` object.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/customization/custom-nodes.mdx#_snippet_2

LANGUAGE: javascript
CODE:
```
let nodes = $state.raw([
  {
    id: 'node-1',
    type: 'textUpdater',
    position: { x: 0, y: 0 },
    data: { text: 'some text' },
  },
]);
```

----------------------------------------

TITLE: Using React Flow Components with New Package Name - JSX
DESCRIPTION: This code illustrates the basic usage of React Flow components (`ReactFlow`, `Controls`, `Background`) after migrating to the new `reactflow` package. It shows the updated import paths for the main component and its styles, reflecting the new package structure.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/src/content/react-flow-v11.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
import { ReactFlow, Controls, Background } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

...

<ReactFlow nodes={nodes} edges={edges}>
  <Controls />
  <Background />
</ReactFlow>
```

----------------------------------------

TITLE: Integrating Audio Node Update with Store in JavaScript
DESCRIPTION: This `updateNode` action within the Zustand store calls `updateAudioNode` to handle audio-specific property updates, then updates the `nodes` state in the store. It depends on `updateAudioNode` from `./audio.js` and is part of a `createWithEqualityFn` store.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_21

LANGUAGE: javascript
CODE:
```
import { updateAudioNode } from './audio';

export const useStore = createWithEqualityFn((set, get) => ({
  ...

  updateNode(id, data) {
    updateAudioNode(id, data);
    set({ nodes: ... });
  },

  ...
}));
```

----------------------------------------

TITLE: Defining Custom SVG Shapes for Animated Edges (TSX)
DESCRIPTION: This snippet defines a `shapes` record, which maps string keys to React components that render SVG elements. Each component receives `animateMotionProps` to apply animation along the edge path. This allows for custom visual elements to be animated on an xyflow edge.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/components/edges/animated-svg-edge.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
const shapes = {
  box: ({ animateMotionProps }) => (
    <rect width="5" height="5" fill="#ff0073">
      <animateMotion {...animateMotionProps} />
    </rect>
  ),
} satisfies Record<string, AnimatedSvg>;
```

----------------------------------------

TITLE: Updating React App Component (JSX)
DESCRIPTION: This JSX snippet modifies the `src/App.jsx` file to render a simple `div` with full screen width and height, and padding. This serves as a clean starting point for the application, ready for React Flow components and Tailwind styling.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
function App() {
  return <div className="w-screen h-screen p-8"></div>;
}

export default App;
```

----------------------------------------

TITLE: Installing Tailwind CSS Dependencies (Bash)
DESCRIPTION: This command installs development dependencies for styling. Tailwind CSS is a utility-first CSS framework, while PostCSS and Autoprefixer are used for processing CSS, ensuring compatibility and adding vendor prefixes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_1

LANGUAGE: bash
CODE:
```
npm install -D tailwindcss postcss autoprefixer
```

----------------------------------------

TITLE: Initializing a new Vite Project (Bash)
DESCRIPTION: This command initializes a new project using Vite, a fast build tool. It's the first step to set up the development environment, specifically for a React and TypeScript project as required by shadcn components.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_0

LANGUAGE: bash
CODE:
```
npx create-vite@latest
```

----------------------------------------

TITLE: Resuming/Suspending AudioContext on User Interaction in React
DESCRIPTION: This snippet adds a function `toggleAudio` to manage the AudioContext's state, allowing it to resume if suspended or suspend if active. This is crucial for Web Audio applications to bypass browser autoplay policies, as AudioContexts often start in a suspended state. The function is then attached to an `onClick` event handler on the main div, ensuring user interaction initiates audio playback.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_3

LANGUAGE: javascript
CODE:
```
const toggleAudio = () => {
  if (context.state === 'suspended') {
    context.resume();
  } else {
    context.suspend();
  }
};

export default function App() {
  return (
    <div ...
      onClick={toggleAudio}
    />
  );
};
```

----------------------------------------

TITLE: Basic Straight SVG Path String
DESCRIPTION: This snippet shows a simple SVG path string representing a straight line between two points (x1, y1) and (x2, y2) using the Move To (M) and Line To (L) commands.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/customization/custom-edges.mdx#_snippet_3

LANGUAGE: svg-path
CODE:
```
M x1 y1 L x2 y2
```

----------------------------------------

TITLE: Using getIncomers in Svelte
DESCRIPTION: This snippet demonstrates how to use the `getIncomers` function from `@xyflow/svelte`. It initializes empty `nodes` and `edges` state variables and then calls `getIncomers` with a sample node and the state values to find incoming nodes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/utils/get-incomers.mdx#_snippet_0

LANGUAGE: ts
CODE:
```
import { getIncomers } from '@xyflow/svelte';

let nodes = $state.raw([]);
let edges = $state.raw([]);

const incomers = getIncomers(
  { id: '1', position: { x: 0, y: 0 }, data: { label: 'node' } },
  nodes.value,
  edges.value,
);
```

----------------------------------------

TITLE: Using Deprecated onLoad Callback (JSX)
DESCRIPTION: This snippet demonstrates the usage of the `onLoad` callback, which has been renamed to `onInit`. The `onLoad` callback was previously used to interact with the React Flow instance once it was initialized, for actions like zooming or fitting the view.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_10

LANGUAGE: jsx
CODE:
```
const onLoad = (reactFlowInstance) => reactFlowInstance.zoomTo(2);
...
<ReactFlow
   ...
  onLoad={onLoad}
/>
```

----------------------------------------

TITLE: Applying Inline Styles to React Flow (JSX)
DESCRIPTION: This JSX snippet demonstrates applying inline CSS styles to the `ReactFlow` component using the `style` prop. It allows for direct customization of the component's appearance, such as background color, width, and height, without external stylesheets.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/customization/theming.mdx#_snippet_3

LANGUAGE: jsx
CODE:
```
import ReactFlow from '@xyflow/react'

const styles = {
  background: 'red',
  width: '100%',
  height: 300,
};

export default function Flow() {
  return <ReactFlow style={styles} nodes={[...]} edges={[...]} />
}
```

----------------------------------------

TITLE: Defining Hidden Node (Old API) - JavaScript
DESCRIPTION: This snippet demonstrates the deprecated `isHidden` boolean property for nodes. Setting it to `true` would hide the node, but it has been renamed to `hidden` for consistency across all boolean node and edge options.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_16

LANGUAGE: js
CODE:
```
const hiddenNode = { id: '1', isHidden: true, position: { x: 50, y: 50 } };
```

----------------------------------------

TITLE: Typing Custom Edges with useEdgesState in TypeScript
DESCRIPTION: This snippet illustrates how to use a generic type argument with the `useEdgesState` hook in TypeScript to define custom edge types. By passing `CustomEdgeType` as a generic, it ensures type-safe management of edges with specific properties, enhancing developer experience and preventing type-related errors.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-edges-state.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
const nodes = useEdgesState<CustomEdgeType>();
```

----------------------------------------

TITLE: Importing React Flow v11 (New API)
DESCRIPTION: This snippet illustrates the new way of importing React Flow in version 11. The package name has changed to `reactflow`, and the main component is now imported as a named export from `@xyflow/react`. This is the recommended approach for v11 and later.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v11.mdx#_snippet_1

LANGUAGE: javascript
CODE:
```
// npm install reactflow
import { ReactFlow } from '@xyflow/react';
```

----------------------------------------

TITLE: Accessing Store with New Zustand API (JSX)
DESCRIPTION: This snippet demonstrates the updated way to access the internal React Flow store using the `useStore` hook, reflecting the migration to Zustand for state management. It allows components to subscribe to specific parts of the store.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_8

LANGUAGE: jsx
CODE:
```
import { useStore } from 'react-flow-renderer';

...
const transform = useStore((store) => store.transform);
```

----------------------------------------

TITLE: Updating Nodes with Mutation (Old API)
DESCRIPTION: This snippet demonstrates the old method of updating nodes in React Flow 11 by directly mutating node objects within the `setNodes` callback. This approach is no longer supported in v12 due to changes in how updates are handled.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_7

LANGUAGE: javascript
CODE:
```
setNodes((currentNodes) =>
  currentNodes.map((node) => {
    node.hidden = true;
    return node;
  })
);
```

----------------------------------------

TITLE: Accessing Node Measured Dimensions (New API)
DESCRIPTION: This snippet shows how to access measured node dimensions in React Flow 12. Measured values are now stored under `node.measured.width` and `node.measured.height` to differentiate them from fixed `node.width` and `node.height` properties.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_4

LANGUAGE: javascript
CODE:
```
// getting the measured width and height
const nodeWidth = node.measured?.width;
const nodeHeight = node.measured?.height;
```

----------------------------------------

TITLE: Importing ApiReferenceSummary Component (JavaScript/TypeScript)
DESCRIPTION: This snippet imports the `ApiReferenceSummary` component from the `xy-shared/server` module. This component is designed to display a summarized view of API references, making it suitable for generating documentation pages. It is typically used in server-side rendering environments.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/index.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
import { ApiReferenceSummary } from 'xy-shared/server';
```

----------------------------------------

TITLE: Updating Hook Values (e.g., useNodes) - New API (Svelte)
DESCRIPTION: Demonstrates that for specific hooks like `useNodes`, `useEdges`, and `useViewport`, the reactive value within the `.current` property can be directly reassigned to update the state.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_11

LANGUAGE: svelte
CODE:
```
const nodes = useNodes();

function updateNodes() {
   nodes.current = [...]
}
```

----------------------------------------

TITLE: Importing Svelte Flow and Tailwind Styles (JS)
DESCRIPTION: This JavaScript snippet demonstrates the recommended import order when using both Svelte Flow's default styles and Tailwind CSS. Importing Tailwind last ensures that its utility classes can correctly override Svelte Flow's defaults.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/customization/theming.mdx#_snippet_4

LANGUAGE: js
CODE:
```
import '@xyflow/svelte/dist/style.css';
import 'tailwind.css';
```

----------------------------------------

TITLE: Initializing React Project with Vite
DESCRIPTION: This command uses npm to scaffold a new React application with Vite. It sets up the basic project structure, including dependencies and configuration for development, providing a quick start for React projects.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_0

LANGUAGE: bash
CODE:
```
npm create vite@latest -- --template react
```

----------------------------------------

TITLE: Custom Handle with Connection Validation (Svelte)
DESCRIPTION: Illustrates creating a reusable custom handle component in Svelte. It wraps the <Handle /> component and uses the isValidConnection prop to restrict connections based on the source node ID.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/handle.mdx#_snippet_1

LANGUAGE: svelte
CODE:
```
<script lang="ts">
	import { Handle, type HandleProps  } from '@xyflow/svelte';

	let { source, ...rest } : HandleProps = $props();

	function isValidConnection(connection) {
		return connection.source === source;
	}
</script>

<Handle
	type="target"
	{isValidConnection}
	{...rest}
/>
```

----------------------------------------

TITLE: Importing and Using useSvelteFlow Hook in Svelte
DESCRIPTION: This snippet demonstrates how to import the useSvelteFlow hook from the @xyflow/svelte library and destructure specific functions (screenToFlowPosition, zoomIn) for use within a Svelte component's script block.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/hooks/use-svelte-flow.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
<script lang="ts">
  import { useSvelteFlow } from '@xyflow/svelte';

  const { screenToFlowPosition, zoomIn } = useSvelteFlow();
</script>
```

----------------------------------------

TITLE: Install Project Dependencies
DESCRIPTION: Command to install the necessary npm packages for the mind map application, including `reactflow`, `zustand` for state management, `classcat` for conditional class names, and `nanoid` for unique IDs.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_2

LANGUAGE: bash
CODE:
```
npm install reactflow zustand classcat nanoid
```

----------------------------------------

TITLE: Projecting Screen Coordinates to Flow (New) - JavaScript
DESCRIPTION: This snippet demonstrates the new `reactFlowInstance.screenToFlowPosition` method. It simplifies the projection of screen coordinates to flow coordinates by directly accepting `event.clientX` and `event.clientY`, eliminating the need for manual bound subtraction.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/whats-new/2023-11-10.mdx#_snippet_1

LANGUAGE: JavaScript
CODE:
```
const position = reactFlowInstance.screenToFlowPosition({
  x: event.clientX,
  y: event.clientY,
});
```

----------------------------------------

TITLE: Importing ReactFlow (Old API)
DESCRIPTION: This snippet demonstrates the old way of importing React Flow components and installing the `reactflow` package before version 12. The package was a default import and required `npm install reactflow`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_1

LANGUAGE: javascript
CODE:
```
// npm install reactflow
import ReactFlow from 'reactflow';
```

----------------------------------------

TITLE: Adding fitView Prop to Svelte Flow (Svelte)
DESCRIPTION: Adds the `fitView` boolean prop to the `SvelteFlow` component. When `fitView` is true, the component automatically adjusts the initial viewport to fit all visible nodes within the canvas area.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/getting-started/building-a-flow.mdx#_snippet_6

LANGUAGE: svelte
CODE:
```
<SvelteFlow bind:nodes bind:edges fitView>
```

----------------------------------------

TITLE: Rendering ApiReferenceSummary Component for Hooks - JSX
DESCRIPTION: This snippet demonstrates how to render the `ApiReferenceSummary` component within a JSX context. The `category` prop is explicitly set to 'hooks' to filter and display only the API documentation relevant to hooks.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/index.mdx#_snippet_1

LANGUAGE: JSX
CODE:
```
<ApiReferenceSummary category="hooks" />
```

----------------------------------------

TITLE: Typing useNodes() with Custom Node Types in TypeScript
DESCRIPTION: Illustrates how to use the `useNodes` hook with a generic type argument in TypeScript to specify custom node types. This provides type safety and better autocompletion for the `nodes` array.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-nodes.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
const nodes = useNodes<CustomNodeType>();
```

----------------------------------------

TITLE: Rendering Edge Labels - Old API (Svelte)
DESCRIPTION: Demonstrates the legacy method of positioning and rendering custom edge labels using the `<EdgeLabelRenderer>` component, which acted as a portal.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_18

LANGUAGE: svelte
CODE:
```
// CustomEdge.svelte
<BaseEdge id={id} path={path} />
<EdgeLabelRenderer>
  <div
    style:transform={`translate(${labelX}px, ${labelY}px)`}
  >
    My Edge Label
  </div>
</EdgeLabelRenderer>
```

----------------------------------------

TITLE: Building Project for Production with pnpm
DESCRIPTION: This command compiles the project for production deployment, optimizing the code and assets. The output is typically found in a 'dist' or 'build' directory, ready for hosting.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/README.md#_snippet_2

LANGUAGE: shell
CODE:
```
pnpm build
```

----------------------------------------

TITLE: Building Project for Production with pnpm
DESCRIPTION: This command compiles and optimizes the entire project for production deployment. It generates static assets and an optimized build, ready to be hosted on a web server.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/README.md#_snippet_2

LANGUAGE: Shell
CODE:
```
$ pnpm build
```

----------------------------------------

TITLE: Bind Nodes/Edges with {prop} in Svelte Flow v0 (Old API)
DESCRIPTION: Illustrates the previous syntax for passing `nodes` and `edges` data to the `<SvelteFlow />` component using standard Svelte prop binding.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_3

LANGUAGE: svelte
CODE:
```
<SvelteFlow {nodes} {edges} />
```

----------------------------------------

TITLE: Rendering ApiReferenceSummary for Components (JSX)
DESCRIPTION: Renders the `ApiReferenceSummary` component, passing `category="components"` as a prop. This configures the component to display API reference documentation specifically for components, likely fetching data based on this category.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/index.mdx#_snippet_1

LANGUAGE: JavaScript
CODE:
```
<ApiReferenceSummary category="components" />
```

----------------------------------------

TITLE: Installing Zustand Package - Bash
DESCRIPTION: This command installs the Zustand state management library as a dependency in your project. Zustand is used to create a central store for managing application state, including React Flow nodes and edges.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/advanced-use/state-management.mdx#_snippet_0

LANGUAGE: bash
CODE:
```
npm install --save zustand
```

----------------------------------------

TITLE: Connecting Audio Nodes in JavaScript
DESCRIPTION: This function establishes a connection between two audio nodes. It retrieves the source and target nodes using their IDs from the `nodes` Map and then calls the `connect` method on the source node, passing the target node as an argument.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_25

LANGUAGE: javascript
CODE:
```
export function connect(sourceId, targetId) {
  const source = nodes.get(sourceId);
  const target = nodes.get(targetId);

  source.connect(target);
}
```

----------------------------------------

TITLE: Using Hooks (e.g., useEdges) - Old API (Svelte)
DESCRIPTION: Illustrates how Svelte Flow hooks like `useEdges` previously returned Svelte writables, which were accessed reactively using the `$` prefix or reactive declarations (`$:`).
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_9

LANGUAGE: svelte
CODE:
```
const edges = useEdges();
$: console.log(edges);
```

----------------------------------------

TITLE: Installing Dependencies for xyflow Project (pnpm)
DESCRIPTION: This command installs all necessary project dependencies using pnpm. It should be run once after cloning the repository or when dependencies change.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/README.md#_snippet_0

LANGUAGE: shell
CODE:
```
pnpm install
```

----------------------------------------

TITLE: Installing Dependencies with pnpm (Shell)
DESCRIPTION: This command installs all project dependencies using pnpm, a fast, disk-space efficient package manager. It should be run after cloning the repository to set up the development environment.
SOURCE: https://github.com/xyflow/web/blob/main/CONTRIBUTING.md#_snippet_0

LANGUAGE: shell
CODE:
```
pnpm install
```

----------------------------------------

TITLE: Starting Development Server with pnpm (Shell)
DESCRIPTION: This command starts the local development server for the project using pnpm. It allows developers to test changes in real-time in a browser.
SOURCE: https://github.com/xyflow/web/blob/main/CONTRIBUTING.md#_snippet_1

LANGUAGE: shell
CODE:
```
pnpm dev
```

----------------------------------------

TITLE: Installing Dependencies with pnpm
DESCRIPTION: This command installs all project dependencies and packages using pnpm, which is the recommended package manager for this monorepo. It ensures all necessary modules are available for local development.
SOURCE: https://github.com/xyflow/web/blob/main/README.md#_snippet_0

LANGUAGE: sh
CODE:
```
pnpm install
```

----------------------------------------

TITLE: Binding Viewport - Old API (Svelte)
DESCRIPTION: Explains the previous method of binding the viewport state to the `<SvelteFlow>` component using a Svelte writable and passing it as a prop.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_12

LANGUAGE: svelte
CODE:
```
const viewport = writable<Viewport>({ x: 100, y: 100, zoom: 1.25 });

<SvelteFlow {viewport} />
```

----------------------------------------

TITLE: Rendering ApiReferenceSummary for Utilities - JSX
DESCRIPTION: Renders the ApiReferenceSummary component, configuring it to display API references specifically categorized as 'utils'. This component dynamically generates content based on the provided category.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/utils/index.mdx#_snippet_1

LANGUAGE: JSX
CODE:
```
<ApiReferenceSummary category="utils" />
```

----------------------------------------

TITLE: Building xyflow Project for Production (pnpm)
DESCRIPTION: This command compiles and optimizes the xyflow website for production deployment. It generates static assets and an optimized build.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/README.md#_snippet_2

LANGUAGE: shell
CODE:
```
pnpm build
```

----------------------------------------

TITLE: Creating New Svelte Project (npx)
DESCRIPTION: Command using npx to create a new Svelte project using the standard Svelte CLI tool. This is a prerequisite step before installing Svelte Flow.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/index.mdx#_snippet_2

LANGUAGE: bash
CODE:
```
npx sv create my-svelte-flow-app
```

----------------------------------------

TITLE: Running Documentation Sites Locally
DESCRIPTION: This command specifically starts the React Flow and Svelte Flow documentation sites for local development, useful when focusing only on the library documentation.
SOURCE: https://github.com/xyflow/web/blob/main/README.md#_snippet_2

LANGUAGE: sh
CODE:
```
pnpm run dev:docs
```

----------------------------------------

TITLE: Starting Development Server with pnpm
DESCRIPTION: This command launches the local development server, making the website accessible at http://localhost:3002. It enables hot-reloading for efficient development and reflects changes made to the source code in real-time.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/README.md#_snippet_1

LANGUAGE: Shell
CODE:
```
$ pnpm start
```

----------------------------------------

TITLE: Starting Development Server for xyflow Project (pnpm)
DESCRIPTION: This command starts a local development server for the xyflow website, typically accessible at http://localhost:3001. It enables live reloading for development.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/README.md#_snippet_1

LANGUAGE: shell
CODE:
```
pnpm start
```

----------------------------------------

TITLE: Removing Audio Node in JavaScript
DESCRIPTION: This function removes an audio node from the graph. It retrieves the node by ID, disconnects it, stops any active audio, and then deletes it from the `nodes` Map. It ensures proper cleanup of audio resources.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_22

LANGUAGE: javascript
CODE:
```
export function removeAudioNode(id) {
  const node = nodes.get(id);

  node.disconnect();
  node.stop?.();

  nodes.delete(id);
}
```

----------------------------------------

TITLE: Running Tests with pnpm (Shell)
DESCRIPTION: This command executes the project's test suite using pnpm. Developers should run this to ensure their changes do not introduce regressions and to validate new features.
SOURCE: https://github.com/xyflow/web/blob/main/CONTRIBUTING.md#_snippet_2

LANGUAGE: shell
CODE:
```
pnpm test
```

----------------------------------------

TITLE: Initializing Web Audio Context and Node Map (JavaScript)
DESCRIPTION: This JavaScript snippet from `./src/audio.js` initializes the Web Audio API by creating a new `AudioContext`. It also declares an empty `Map` named `nodes`, which is intended to store and manage references to various audio nodes within the application's audio graph.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_18

LANGUAGE: JavaScript
CODE:
```
const context = new AudioContext();
const nodes = new Map();
```

----------------------------------------

TITLE: Scaffolding New React/Svelte Example (pnpm scaffold) - Shell
DESCRIPTION: This command generates the necessary file structure for a new React or Svelte example within the project. It scaffolds out all required files, including the entry `index.html` and a minimal React or Svelte component, streamlining the setup of new examples.
SOURCE: https://github.com/xyflow/web/blob/main/apps/example-apps/README.md#_snippet_1

LANGUAGE: Shell
CODE:
```
pnpm scaffold
```

----------------------------------------

TITLE: Getting Handle Connections with useHandleConnections (JSX)
DESCRIPTION: This example demonstrates how to use the `useHandleConnections` hook to retrieve an array of connections for a specific handle. It shows how to filter connections by `type` (e.g., 'target') and `id` (e.g., 'my-handle') and then display the count of incoming connections. Note that this hook is deprecated.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/use-handle-connections.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useHandleConnections } from '@xyflow/react';

export default function () {
  const connections = useHandleConnections({ type: 'target', id: 'my-handle' });

  return (
    <div>There are currently {connections.length} incoming connections!</div>
  );
}
```

----------------------------------------

TITLE: Defining Custom Node/Edge Props - Old API (Svelte)
DESCRIPTION: Demonstrates the previous method for typing and accessing props in custom Svelte Flow nodes or edges using Svelte's legacy `$$Props` type and `export let`.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_7

LANGUAGE: svelte
CODE:
```
// CustomNode.svelte

type $$Props = NodeProps;

export let data: $$Props['data'];
export let position: $$Props['position'];
export let selected: $$Props['selected'];
```

----------------------------------------

TITLE: Starting Development Server with Vite (pnpm dev) - Shell
DESCRIPTION: This command initiates the Vite development server, enabling hot reloading for xyflow example applications. It includes a custom plugin to regenerate `source.json` upon example updates, though it does not hot-reload external consumers or regenerate `source.json` for changes in non-active examples.
SOURCE: https://github.com/xyflow/web/blob/main/apps/example-apps/README.md#_snippet_0

LANGUAGE: Shell
CODE:
```
pnpm dev
```

----------------------------------------

TITLE: Integrating Audio Toggle with Zustand Store in JavaScript
DESCRIPTION: This snippet integrates the `toggleAudio` functionality into a Zustand store. It updates the `isRunning` state of the store only after the asynchronous `toggleAudio` operation (suspending or resuming the AudioContext) has completed, ensuring state consistency.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_28

LANGUAGE: javascript
CODE:
```
import { ..., isRunning, toggleAudio } from './audio'

export const useStore = createWithEqualityFn((set, get) => ({
  ...

  isRunning: isRunning(),

  toggleAudio() {
    toggleAudio().then(() => {
      set({ isRunning: isRunning() });
    });
  }
}));
```

----------------------------------------

TITLE: Setting Node Dimensions via Style (Old API)
DESCRIPTION: This snippet demonstrates how node dimensions were typically set using the `node.style` property in React Flow 11. In v11, `node.width` and `node.height` were measured values, not for setting fixed dimensions.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_5

LANGUAGE: javascript
CODE:
```
// in React Flow 11 you might have used node.style to set the dimensions
const nodes = [
  {
    id: '1',
    type: 'input',
    data: { label: 'input node' },
    position: { x: 250, y: 5 },
    style: { width: 180, height: 40 }
  }
];
```

----------------------------------------

TITLE: Using Old `onEdgeUpdate` API in ReactFlow (v11)
DESCRIPTION: This snippet demonstrates the deprecated `onEdgeUpdate` API for handling edge updates in ReactFlow, including `onEdgeUpdateStart` and `onEdgeUpdateEnd`. These props are used to manage the lifecycle of an edge update operation.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_9

LANGUAGE: javascript
CODE:
```
<ReactFlow
  onEdgeUpdate={onEdgeUpdate}
  onEdgeUpdateStart={onEdgeUpdateStart}
  onEdgeUpdateEnd={onEdgeUpdateEnd}
/>
```

----------------------------------------

TITLE: Defining Edge with Arrow Head (Old API) - JavaScript
DESCRIPTION: This snippet illustrates the deprecated `arrowHeadType` property for edges, used to specify the type of arrow marker at the end of an edge. It has been replaced by more flexible `markerStart` and `markerEnd` properties for enhanced customization.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_18

LANGUAGE: js
CODE:
```
const markerEdge = { source: '1', target: '2', arrowHeadType: 'arrow' };
```

----------------------------------------

TITLE: Defining Nodes with Deprecated `parentNode` in ReactFlow (v11)
DESCRIPTION: This snippet illustrates how nodes were defined in ReactFlow v11 using the `parentNode` property to specify the ID of a parent node for subflows. This property has been renamed for clarity.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_11

LANGUAGE: javascript
CODE:
```
const nodes = [
  // some nodes ...
  {
    id: 'xyz-id',
    position: { x: 0, y: 0 },
    type: 'default',
    data: {},
    parentNode: 'abc-id',
  },
];
```

----------------------------------------

TITLE: Importing React Flow v10 (Old API)
DESCRIPTION: This snippet demonstrates the old way of importing React Flow in version 10. It shows the package name `react-flow-renderer` used for installation and import. This API is deprecated in v11 and should be updated.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v11.mdx#_snippet_0

LANGUAGE: javascript
CODE:
```
// npm install react-flow-renderer
import ReactFlow from 'react-flow-renderer';
```

----------------------------------------

TITLE: Building Project with Vite (pnpm build) - Shell
DESCRIPTION: This command executes the `vite build` process for the xyflow project. It utilizes the same plugin as the development server to generate `source.json` files at the beginning of the build process.
SOURCE: https://github.com/xyflow/web/blob/main/apps/example-apps/README.md#_snippet_2

LANGUAGE: Shell
CODE:
```
pnpm build
```

----------------------------------------

TITLE: Apply Basic Body and Header Styling with CSS
DESCRIPTION: Provides CSS rules to set basic page styling. It removes the default body margin, sets a light background color for the entire page, and changes the text color for elements with the class `header` to a subtle grey.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_27

LANGUAGE: css
CODE:
```
body {
  margin: 0;
  background-color: #f8f8f8;
  height: 100%;
}

.header {
  color: #cdcdcd;
}
```

----------------------------------------

TITLE: Developing New Components with pnpm (Bash)
DESCRIPTION: This command initiates the development process for a new React Flow component. Running `pnpm add-component <component-name>` automatically sets up the necessary file structure and boilerplate code, streamlining the creation of new components.
SOURCE: https://github.com/xyflow/web/blob/main/apps/ui-components/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
pnpm add-component <component-name>
```

----------------------------------------

TITLE: Accessing Node Dimensions (Old API)
DESCRIPTION: This snippet illustrates how node dimensions (width and height) were accessed directly from the `node` object in React Flow 11. These values represented the measured dimensions of the node.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_3

LANGUAGE: javascript
CODE:
```
// getting the measured width and height
const nodeWidth = node.width;
const nodeHeight = node.height;
```

----------------------------------------

TITLE: Using Deprecated `getNodesBounds` Signature (v11)
DESCRIPTION: This snippet illustrates the old function signature for `getNodesBounds` in ReactFlow v11, where `nodeOrigin` was passed as a direct second parameter. This signature has been updated to use an options object.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_15

LANGUAGE: javascript
CODE:
```
const bounds = getNodesBounds(nodes: Node[], nodeOrigin)
```

----------------------------------------

TITLE: Controlling AudioContext State in JavaScript
DESCRIPTION: These functions manage the Web Audio API's AudioContext state. `isRunning` checks if the context is active, while `toggleAudio` asynchronously suspends or resumes the context based on its current state. This prevents premature store updates by returning a Promise.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_27

LANGUAGE: javascript
CODE:
```
export function isRunning() {
  return context.state === 'running';
}

export function toggleAudio() {
  return isRunning() ? context.suspend() : context.resume();
}
```

----------------------------------------

TITLE: Running All Applications Locally
DESCRIPTION: This command starts all applications and websites within the monorepo simultaneously for local development, providing a quick way to run everything at once.
SOURCE: https://github.com/xyflow/web/blob/main/README.md#_snippet_1

LANGUAGE: sh
CODE:
```
pnpm run dev
```

----------------------------------------

TITLE: Disabling Pan on Drag (Old API) - JSX
DESCRIPTION: This snippet shows the deprecated `paneMoveable` prop used to control whether the canvas can be panned by dragging. Setting it to `false` disables this interaction, but it has been replaced by a more consistently named prop.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_12

LANGUAGE: jsx
CODE:
```
<ReactFlow
   ...
  paneMoveable={false}
/>
```

----------------------------------------

TITLE: Accessing Store with Old Redux API (JSX)
DESCRIPTION: This snippet illustrates the deprecated method of accessing the internal React Flow store using `useStoreState` and `useStoreActions`. This API was part of the Redux-based state management system, which has since been replaced.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_7

LANGUAGE: jsx
CODE:
```
import { useStoreState, useStoreActions } from 'react-flow-renderer';

...

const transform = useStoreState((store) => store.transform);
```

----------------------------------------

TITLE: Installing Svelte Flow Alpha with npm
DESCRIPTION: This snippet demonstrates how to install the alpha version of Svelte Flow using npm. Svelte Flow is a port of React Flow to Svelte, currently in alpha, and its API is subject to change. This installation command adds the @xyflow/svelte package to your project's dependencies.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/src/content/svelte-flow-alpha-xyflow.mdx#_snippet_0

LANGUAGE: Bash
CODE:
```
npm install @xyflow/svelte
```

----------------------------------------

TITLE: Rendering ExamplesOverview Component - JSX
DESCRIPTION: This snippet renders the `ExamplesOverview` React component using JSX syntax. As a self-closing tag, it indicates that the component does not accept children and is responsible for displaying the collection of React Flow examples on the page.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/app/(content-pages)/examples/page.mdx#_snippet_1

LANGUAGE: JSX
CODE:
```
<ExamplesOverview />
```

----------------------------------------

TITLE: Managing Elements in React Flow v9 (Old API)
DESCRIPTION: This snippet demonstrates the old API for managing nodes and edges in React Flow v9. It uses a single `elements` array to hold both nodes and edges, and relies on `onElementsRemove` and `onConnect` callbacks to update this array. The `removeElements` and `addEdge` helpers are used to mutate the `elements` state, representing a semi-controlled approach.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import { useState, useCallback } from 'react';
import { ReactFlow, removeElements, addEdge } from 'react-flow-renderer';

const initialElements = [
  { id: '1', data: { label: 'Node 1' }, position: { x: 250, y: 0 } },
  { id: '2', data: { label: 'Node 2' }, position: { x: 150, y: 100 } },
  { id: 'e1-2', source: '1', target: '2' },
];

const BasicFlow = () => {
  const [elements, setElements] = useState(initialElements);
  const onElementsRemove = useCallback(
    (elementsToRemove) =>
      setElements((els) => removeElements(elementsToRemove, els)),
    [],
  );
  const onConnect = useCallback((connection) =>
    setElements((es) => addEdge(connection, es)),
  );

  return (
    <ReactFlow
      elements={elements}
      onElementsRemove={onElementsRemove}
      onConnect={onConnect}
    />
  );
};

export default BasicFlow;
```

----------------------------------------

TITLE: Accessing Deprecated `nodeInternals` from Store (v11)
DESCRIPTION: This snippet shows the deprecated way of accessing node data using `s.nodeInternals.get(id)` from the ReactFlow store in v11. This property has been renamed for clarity.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v12.mdx#_snippet_20

LANGUAGE: javascript
CODE:
```
const node = useStore((s) => s.nodeInternals.get(id));
```

----------------------------------------

TITLE: Calculating Bezier Edge Path and Center (v10)
DESCRIPTION: This snippet shows the v10 API for generating a Bezier edge path using `getBezierPath` and calculating its center point using `getBezierEdgeCenter`. In v11, these helper functions have been updated, and the path creation function now directly returns the center/label position.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v11.mdx#_snippet_5

LANGUAGE: jsx
CODE:
```
import { getBezierEdgeCenter, getBezierPath } from 'react-flow-renderer';

const path = getBezierPath(edgeParams);
const [centerX, centerY] = getBezierEdgeCenter(params);
```

----------------------------------------

TITLE: Configuring Next.js for @xyflow/xy-ui Transpilation (JavaScript)
DESCRIPTION: This snippet shows how to modify `next.config.js` to include `@xyflow/xy-ui` in the `transpilePackages` array. This is crucial for Next.js to correctly process and bundle the UI components from the package, preventing potential build errors related to module resolution or syntax.
SOURCE: https://github.com/xyflow/web/blob/main/packages/xy-ui/README.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
transpilePackages: ['@xyflow/xy-ui']
```

----------------------------------------

TITLE: Creating Web Audio Nodes Dynamically in JavaScript
DESCRIPTION: This function dynamically creates Web Audio API nodes based on a specified `type` and `data`. It supports 'osc' (OscillatorNode) and 'amp' (GainNode), setting their initial properties like frequency, type, or gain, and storing them by `id` for later reference.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_29

LANGUAGE: javascript
CODE:
```
export function createAudioNode(id, type, data) {
  switch (type) {
    case 'osc': {
      const node = context.createOscillator();
      node.frequency.value = data.frequency;
      node.type = data.type;
      node.start();

      nodes.set(id, node);
      break;
    }

    case 'amp': {
      const node = context.createGain();
      node.gain.value = data.gain;

      nodes.set(id, node);
      break;
    }
  }
}
```

----------------------------------------

TITLE: Importing ApiReferenceSummary Component (JavaScript)
DESCRIPTION: Imports the `ApiReferenceSummary` component from the `xy-shared/server` module, making it available for use in the current file. This component is typically used to render structured API documentation.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/components/index.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { ApiReferenceSummary } from 'xy-shared/server';
```

----------------------------------------

TITLE: Adding Audio Toggle State and Action to Zustand Store (JavaScript)
DESCRIPTION: This JavaScript snippet extends the Zustand store in `./src/store.js` by adding an `isRunning` boolean state and a `toggleAudio` action. The `toggleAudio` function updates the `isRunning` state, which can be used to control audio processing, demonstrating how to manage application-wide flags.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/react-flow-and-the-web-audio-api.mdx#_snippet_16

LANGUAGE: JavaScript
CODE:
```
const useStore = createWithEqualityFn((set, get) => ({
  ...

  isRunning: false,

  toggleAudio() {
    set({ isRunning: !get().isRunning });
  },

  ...
}));
```

----------------------------------------

TITLE: Projecting Screen Coordinates to Flow (Deprecated) - JavaScript
DESCRIPTION: This snippet shows the deprecated `reactFlowInstance.project` method. It required manually calculating the relative client coordinates by subtracting the React Flow wrapper's bounds before projecting them to flow coordinates. This approach is less intuitive and prone to errors.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/whats-new/2023-11-10.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();

const position = reactFlowInstance.project({
  x: event.clientX - reactFlowBounds.left,
  y: event.clientY - reactFlowBounds.top,
});
```

----------------------------------------

TITLE: Setting Viewport with `useZoomPanHelper` (Old API) - JavaScript
DESCRIPTION: This snippet illustrates the deprecated `transform` function from `useZoomPanHelper` for setting the viewport. It allows specifying x, y coordinates and zoom level, but has been replaced by `setViewport` for clarity and consistency.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v10.mdx#_snippet_14

LANGUAGE: js
CODE:
```
const { transform, setCenter, setZoom  } = useZoomPanHelper();
...
transform({ x: 100, y: 100, zoom: 2 });
```

----------------------------------------

TITLE: Setting Initial Viewport with defaultPosition/defaultZoom (v10)
DESCRIPTION: This JSX snippet demonstrates how to set the initial position and zoom level of the React Flow viewport using `defaultPosition` and `defaultZoom` props in v10. These props are now deprecated in v11 and have been replaced by a single `defaultViewport` prop.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v11.mdx#_snippet_3

LANGUAGE: jsx
CODE:
```
import ReactFlow from 'react-flow-renderer';

const Flow = () => {
  return <ReactFlow defaultPosition={[10, 15]} defaultZoom={5} />;
};

export default Flow;
```

----------------------------------------

TITLE: Adding Popover Component with shadcn-ui CLI
DESCRIPTION: This command uses the `shadcn-ui` CLI to automatically add a new `<Popover />` component to the project. It simplifies the vending process by fetching the component's source code and integrating it into the local UI component directory. After running, manual steps are required to update import paths and export the component.
SOURCE: https://github.com/xyflow/web/blob/main/packages/xy-ui/components/ui/README.md#_snippet_0

LANGUAGE: sh
CODE:
```
pnpm dlx shadcn-ui@latest add popover
```

----------------------------------------

TITLE: Importing UI Components and Shared Blog Post Preview
DESCRIPTION: This snippet imports necessary components from `@xyflow/xy-ui` for layout and `xy-shared` for displaying blog post previews. These components are used to structure content grids and display article information within the application.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/index.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { ContentGrid, ContentGridItem } from '@xyflow/xy-ui';
import { BlogPostPreview } from 'xy-shared';
```

----------------------------------------

TITLE: Default Configuration Prompts for shadcn/ui CLI (Text Output)
DESCRIPTION: This snippet shows the typical interactive prompts and default responses when running `npx shadcn@latest init`. It illustrates the choices for style, base color, and CSS variable usage for theming. These selections determine the initial look and feel of shadcn components and how they integrate with Tailwind CSS.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/getting-started-with-react-flow-components.mdx#_snippet_10

LANGUAGE: text
CODE:
```
✔ Which style would you like to use? › New York
✔ Which color would you like to use as the base color? › Neutral
✔ Would you like to use CSS variables for theming? yes
```

----------------------------------------

TITLE: Updating Preview Screenshots (pnpm screenshots) - Shell
DESCRIPTION: This command updates all preview screenshots for the React and Svelte examples located under the `/react` and `/svelte` directories, ensuring visual assets are current.
SOURCE: https://github.com/xyflow/web/blob/main/apps/example-apps/README.md#_snippet_3

LANGUAGE: Shell
CODE:
```
pnpm screenshots
```

----------------------------------------

TITLE: Using Custom Connection Line - Old API (Svelte)
DESCRIPTION: Describes the legacy method of providing a custom connection line component to `<SvelteFlow>` using a named slot.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/learn/troubleshooting/migrate-to-v1.mdx#_snippet_14

LANGUAGE: svelte
CODE:
```
<SvelteFlow {nodes} {edges}>
  <ConnectionLine slot="connectionLine" />
  <Background variant={BackgroundVariant.Lines} />
</SvelteFlow>
```

----------------------------------------

TITLE: Fitting View After Node Addition (Old Method) - React Flow - TypeScript
DESCRIPTION: This snippet demonstrates the previous workaround for ensuring `fitView` correctly adjusts the view after new nodes are added or updated. It required wrapping `fitView()` in `requestAnimationFrame` to allow the DOM to render the new nodes before the view adjustment, preventing a brief moment of unfitted display.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/whats-new/2025-03-27.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
setNodes((nodes) => [nodes, ...newNode]);
requestAnimationFrame(() => {
  fitView();
});
```

----------------------------------------

TITLE: Using ApiReferenceSummary for Types Documentation (JSX)
DESCRIPTION: This snippet demonstrates the usage of the `ApiReferenceSummary` component within a JSX context. By setting the `category` prop to 'types', it specifically filters and renders documentation related to data types. This allows for modular and categorized display of API reference information.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/types/index.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
<ApiReferenceSummary category="types" />
```

----------------------------------------

TITLE: Importing ApiReferenceSummary Component (TypeScript)
DESCRIPTION: This snippet imports the `ApiReferenceSummary` component from the `xy-shared/server` module. This component is designed to display a summary of API references, likely for different categories of documentation.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/index.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
import { ApiReferenceSummary } from 'xy-shared/server';
```

----------------------------------------

TITLE: Importing ExamplesOverview Component - JavaScript/TypeScript
DESCRIPTION: This snippet imports the `ExamplesOverview` component from the local `@/components/examples-overview` path. This makes the component available for use within the current React component or page, following standard ES module import syntax.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/app/(content-pages)/examples/page.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { ExamplesOverview } from '@/components/examples-overview';
```

----------------------------------------

TITLE: Importing Image Component - JavaScript
DESCRIPTION: This snippet imports the `Image` component from the `xy-shared` library, making it available for use within the current file. This is a standard ES module import syntax, essential for modularizing code and reusing components across a project.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/whats-new/2023-12-06.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { Image } from 'xy-shared';
```

----------------------------------------

TITLE: Scaffold React App with Vite (JavaScript)
DESCRIPTION: Command to create a new React project using Vite with the default JavaScript template. This is the initial step for setting up the project structure.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/tutorials/mind-map-app-with-react-flow.mdx#_snippet_0

LANGUAGE: bash
CODE:
```
npm create vite@latest reactflow-mind-map -- --template react
```

----------------------------------------

TITLE: Importing QandA Component in React
DESCRIPTION: This snippet demonstrates how a `QandA` component is imported from a local path within a React application. It's a standard pattern for module inclusion, making the component available for use in the current file. This specific import is used to structure the case study content.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/pro/case-studies/doubleloop-case-study.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import QandA from '@/components/case-study-qa';
```

----------------------------------------

TITLE: Handling Connection Stop Events in react-flow-renderer (Old API)
DESCRIPTION: This code illustrates the deprecated `onConnectStop` and `onClickConnectStop` event handlers used in `react-flow-renderer` (v10 and earlier). These props were used to trigger actions when a connection interaction concluded.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/learn/troubleshooting/migrate-to-v11.mdx#_snippet_7

LANGUAGE: jsx
CODE:
```
import ReactFlow from 'react-flow-renderer';

const Flow = () => {
  const onConnectStop = () => console.log('on connect stop');

  return (
    <ReactFlow
      defaultNodes={defaultNodes}
      defaultEdges={defaultEdges}
      onConnectStop={onConnectStop}
      onClickConnectStop={onConnectStop}
    />
  );
};

export default Flow;
```

----------------------------------------

TITLE: Rendering ApiReferenceSummary Component (JSX)
DESCRIPTION: This snippet demonstrates how to render the `ApiReferenceSummary` component within a JSX context. The `category` prop is set to 'components', indicating that the component should display API references specifically for UI components.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/components/index.mdx#_snippet_1

LANGUAGE: jsx
CODE:
```
<ApiReferenceSummary category="components" />
```

----------------------------------------

TITLE: Starting Development Server with pnpm
DESCRIPTION: This command initiates the development server, making the application accessible locally at http://localhost:3003. It's used for local development and testing purposes.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/README.md#_snippet_1

LANGUAGE: shell
CODE:
```
pnpm start
```

----------------------------------------

TITLE: Installing Project Dependencies with pnpm
DESCRIPTION: This command installs all necessary project dependencies using the pnpm package manager. It should be run once after cloning the repository to set up the project environment.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/README.md#_snippet_0

LANGUAGE: shell
CODE:
```
pnpm install
```

----------------------------------------

TITLE: Importing ApiReferenceSummary Component (JavaScript)
DESCRIPTION: This snippet imports the `ApiReferenceSummary` component from the `xy-shared/server` module, making it available for use in the current file. This component is typically used to display a summary of API references.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/index.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { ApiReferenceSummary } from 'xy-shared/server';
```

----------------------------------------

TITLE: Rendering ApiReferenceSummary Component (JSX)
DESCRIPTION: This snippet renders the `ApiReferenceSummary` component, passing a `category` prop with the value 'types'. This indicates that the component should display API reference summaries specifically for types, dynamically pulling relevant documentation.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/types/index.mdx#_snippet_1

LANGUAGE: JSX
CODE:
```
<ApiReferenceSummary category="types" />
```

----------------------------------------

TITLE: Dynamically Listing xyflow Examples with JavaScript
DESCRIPTION: This JavaScript snippet defines a utility function `addLink` to create and append list items with links. It then fetches example paths from `/all.json`, parsing the 'react' and 'svelte' arrays to dynamically populate corresponding HTML lists (`examples-react` and `examples-svelte`) with links to individual examples.
SOURCE: https://github.com/xyflow/web/blob/main/apps/example-apps/index.html#_snippet_0

LANGUAGE: JavaScript
CODE:
```
const addLink = (container, prefix, path) => {
  const li = document.createElement('li');
  const href = path + '/index.html';
  const label = path.replace(prefix, '');
  li.innerHTML = `<a href="${href}" class="hover:underline">${label}</a>`;
  container.appendChild(li);
};
fetch('/all.json')
  .then((res) => res.json())
  .then(({ react, svelte }) => {
    const reactList = document.getElementById('examples-react');
    react.forEach((path) => {
      addLink(reactList, 'react/', path);
    });
    const svelteList = document.getElementById('examples-svelte');
    svelte.forEach((path) => {
      addLink(svelteList, 'svelte/', path);
    });
  });
```

----------------------------------------

TITLE: Importing ApiReferenceSummary Component - TypeScript
DESCRIPTION: Imports the ApiReferenceSummary component from the xy-shared/server module. This component is designed to aggregate and display summaries of API references, likely for server-side rendering contexts.
SOURCE: https://github.com/xyflow/web/blob/main/sites/svelteflow.dev/src/content/api-reference/utils/index.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { ApiReferenceSummary } from 'xy-shared/server';
```

----------------------------------------

TITLE: Importing ApiReferenceSummary Component - JavaScript/TypeScript
DESCRIPTION: This snippet imports the `ApiReferenceSummary` component from the `xy-shared/server` module. This component is crucial for dynamically generating and displaying API documentation content on the page.
SOURCE: https://github.com/xyflow/web/blob/main/sites/reactflow.dev/src/content/api-reference/hooks/index.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { ApiReferenceSummary } from 'xy-shared/server';
```

----------------------------------------

TITLE: Unpublishing a Specific npm Package Version
DESCRIPTION: This command is used to unpublish a specific version (0.1.0) of an npm package. The `--force` flag is necessary when unpublishing the last remaining version of a package, but this action can inadvertently trigger npm's security features, preventing the package name from being reused.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/src/content/reactflow-npm-package-name.mdx#_snippet_0

LANGUAGE: sh
CODE:
```
npm unpublish@0.1.0 --force
```

----------------------------------------

TITLE: Publishing an npm Package
DESCRIPTION: This command attempts to publish the current project as a new version of an npm package to the registry. In the context of this article, the command failed with an error indicating that the package name was too similar to an existing one, demonstrating the impact of npm's typosquatting protection after a previous unpublish operation.
SOURCE: https://github.com/xyflow/web/blob/main/sites/xyflow.com/src/content/reactflow-npm-package-name.mdx#_snippet_1

LANGUAGE: sh
CODE:
```
npm publish
```