**Project Title:** AI-Powered UI Mockup Generator with React Flow Canvas

**Project Overview:**
We are building a Next.js application that helps UX designers iterate quickly by generating UI mockups using AI. The core interface will be a canvas powered by @xyflow/react (formerly React Flow). Users will input a text prompt, an AI (simulated for now) will generate HTML with Tailwind CSS, and this HTML will be rendered as a movable, draggable node on the React Flow canvas. Generated mockups (nodes) should be persisted in the user's LocalStorage.

**Core Technologies:**
*   Next.js (App Router)
*   React
*   TypeScript
*   @xyflow/react (for the canvas)
*   Tailwind CSS (for styling the application UI and for the generated mockups)
*   LocalStorage API
*   Sonner (for toast notifications)

**Key Features to Implement (MVP):**

1.  **Next.js App Router Setup:**
    *   A single main page (`app/page.tsx`) that will host the application
    *   Proper client/server component separation with `use client` directives
    *   Ensure Tailwind CSS is correctly configured and working globally

2.  **React Flow Canvas Integration:**
    *   Set up a React Flow instance on the main page as a client component
    *   Wrap the React Flow component with `ReactFlowProvider`
    *   Use the new hooks: `useNodesState` and `useEdgesState` for state management
    *   Include basic controls like `Background` and `MiniMap`
    *   The canvas should fill a significant portion of the screen
    *   Implement proper TypeScript types for nodes and edges
    *   Explicitly define and pass a `nodeTypes` prop to the `<ReactFlow />` component to register custom node components (e.g., `nodeTypes={{ htmlMockupNode: HtmlMockupNode }}`).

3.  **User Input for AI Prompt:**
    *   A simple text input field where the user can type their prompt
    *   A "Generate Mockup" button
    *   Toast notifications for feedback using Sonner

4.  **Simulated AI Generation & Custom Node Creation:**
    *   When the "Generate Mockup" button is clicked:
        *   **Simulate AI Call:** For now, instead of a real AI call, use the user's input prompt to construct a *sample* HTML string with Tailwind classes. For example:
            *   If the user types "button", generate `<button class='bg-blue-500 text-white p-2 rounded'>Generated Button: button</button>`
            *   If they type "card", generate `<div class='bg-white shadow-lg rounded-lg p-4 border border-gray-200'><h2>Generated Card</h2><p>Content for: card</p></div>`
        *   **Create New Node:** Use the generated HTML string to create a new React Flow node object with:
            *   A unique `id` using `nanoid()`
            *   The `type` should be a custom type (e.g., `'htmlMockupNode'`)
            *   The `position` should be a default starting position or slightly offset from the last node
            *   The `data` object containing the `htmlString`
        *   Add this new node to the React Flow `nodes` state using `setNodes`

5.  **Custom React Flow Node (`HtmlMockupNode`):**
    *   Create a client component (e.g., `components/canvas/HtmlMockupNode.tsx`)
    *   This component will receive the `data` prop containing `htmlString`
    *   **Render HTML:** Inside this component, use `dangerouslySetInnerHTML` to render the `htmlString`
    *   **Styling:** The node itself should have:
        *   A light border
        *   Padding
        *   A subtle background if the HTML content doesn't have one
        *   Proper TypeScript types for props
    *   Nodes should be draggable by default (React Flow handles this)
    *   Add a delete button to remove nodes. For interactive elements like buttons within custom nodes, use `className="nodrag"` to prevent unintended node dragging.

6.  **LocalStorage Persistence for Nodes:**
    *   **Saving Nodes:** Use a custom hook that watches the `nodes` array and saves to LocalStorage:
        *   Implement debouncing to avoid excessive writes
        *   Use a specific key (e.g., `uiMockupAppNodes`)
        *   Serialize using `JSON.stringify`
    *   **Loading Nodes:** On application mount:
        *   Load the `nodes` array from LocalStorage
        *   Deserialize using `JSON.parse`
        *   Initialize the React Flow `nodes` state with this data
        *   Handle errors gracefully with toast notifications
    *   **Client-Side Only:** All LocalStorage operations must happen on the client-side
        *   Use `useEffect` appropriately
        *   Ensure components are marked with `'use client'`

7.  **Basic Application Layout & Styling:**
    *   Use Tailwind CSS for the overall application layout
    *   Create a clean, modern interface with:
        *   A header area for the input and controls
        *   A large canvas area for React Flow
        *   Proper spacing and responsive design
    *   Add loading states and error boundaries

**Detailed Implementation & File Structure:**

*   `app/`
    *   `page.tsx` - Main page (server component)
    *   `layout.tsx` - Root layout with providers
*   `components/`
    *   `canvas/`
        *   `FlowCanvas.tsx` - Main React Flow component (client)
        *   `HtmlMockupNode.tsx` - Custom node component (client)
        *   `CanvasControls.tsx` - Canvas control buttons (client)
    *   `ui/`
        *   `Input.tsx` - Prompt input component
        *   `Button.tsx` - Generate button component
*   `hooks/`
    *   `useLocalStorage.ts` - Custom hook for persistence
    *   `useMockupNodes.ts` - Node management hook
*   `lib/`
    *   `mockupGenerator.ts` - Simulated AI HTML generation
    *   `types.ts` - TypeScript types and interfaces

**Important Technical Considerations:**

1. **React Flow v12+ Requirements:**
   * Always wrap React Flow with `ReactFlowProvider`
   * Use the new `@xyflow/react` package
   * Implement proper TypeScript types for nodes and edges
   * Use the new hooks for state management
   * Custom nodes receive `positionAbsoluteX` and `positionAbsoluteY` as props, not `xPos` and `yPos`.
   * For subflows, v12+ uses `parentId` instead of `parentNode`.
   * Consider using the `addEdge` helper for creating edges.
   * Custom nodes receive `positionAbsoluteX` and `positionAbsoluteY` as props (not `xPos` and `yPos`).
   * For subflows, v12+ uses `parentId` instead of `parentNode`.

2. **Client Components:**
   * Mark all React Flow related components with `'use client'`
   * Keep React Flow state management in client components
   * Handle hydration properly

3. **Performance:**
   * Implement debouncing for LocalStorage saves
   * Use proper memoization for callbacks
   * Consider using React.memo for node components

4. **Error Handling:**
   * Implement error boundaries
   * Use toast notifications for user feedback
   * Graceful fallbacks for LocalStorage errors

5. **TypeScript:**
   * Define proper types for nodes and edges
   * Use strict type checking
   * Implement proper prop types for components
   * Suggest considering the use of union types with change handlers like `OnNodesChange<AppNode>` for improved type safety when dealing with multiple node/edge types.

**What NOT to Implement in this MVP:**
*   Actual AI model integration
*   User accounts or cloud-based storage
*   Advanced node resizing
*   Complex edge creation UI
*   Undo/redo functionality beyond simple node deletion

**Acceptance Criteria:**

1.  The Next.js app loads with a React Flow canvas and input controls
2.  Typing a prompt and clicking "Generate" adds a new custom node to the canvas
3.  The custom node correctly renders the simulated HTML content with Tailwind styles
4.  Nodes are draggable on the canvas
5.  Node positions and content are saved to LocalStorage
6.  Upon browser refresh, previously generated nodes are reloaded onto the canvas
7.  Nodes can be deleted, and this change is reflected in LocalStorage
8.  The console should be free of major errors
9.  Toast notifications provide feedback for actions
10. The application is responsive and works on different screen sizes

**Next Steps After MVP:**
1. Add real AI integration
2. Implement undo/redo
3. Add node resizing
4. Add edge creation between nodes
5. Add export functionality
6. Implement cloud storage
7. Add user accounts
8. Add collaboration features