# Product Requirements Document: Enhanced HtmlMockupNode Component

## Introduction/Overview
This feature enhances the existing HtmlMockupNode component with improved interaction capabilities, including node selection, resizing, renaming, and HTML code viewing functionality. The goal is to provide UX designers with better control and insight into their mockups while maintaining a clean, intuitive interface.

## Goals
1. Enable users to select nodes for detailed interaction
2. Allow users to resize nodes for better layout control
3. Provide node naming capabilities for better organization
4. Allow users to view and copy generated HTML code
5. Ensure responsive design across different screen sizes

## User Stories
1. As a UX designer, I want to select a node to perform actions on it, so I can modify its properties efficiently
2. As a UX designer, I want to resize nodes, so I can better organize my mockup layout
3. As a UX designer, I want to name my nodes, so I can keep track of different mockups
4. As a UX designer, I want to view the HTML code of a mockup, so I can understand and reuse the generated code
5. As a mobile user, I want to delete nodes easily, so I can manage my mockups on touch devices

## Functional Requirements

### 1. Node Selection
1.1. The system must allow node selection by clicking anywhere on the node
1.2. The system must visually indicate selected state with subtle visual feedback
1.3. The system must support only single node selection (no multi-select)

### 2. Node Toolbar
2.1. The system must display a floating toolbar above selected nodes
2.2. The toolbar must follow the node when dragged
2.3. The toolbar must contain:
   - Node name display/edit field
   - Button to view HTML code
   - Delete button (mobile only)
2.4. The toolbar must be positioned to avoid overlap with the node content

### 3. Node Resizing
3.1. The system must display resize handles when a node is selected
3.2. The system must support free-form resizing (no aspect ratio lock)
3.3. The system must display current dimensions while resizing
3.4. The system must hide resize handles when node is not selected

### 4. Node Naming
4.1. The system must provide default names for new nodes (e.g., "Mockup 1", "Mockup 2")
4.2. The system must enforce unique names/slugs across all nodes
4.3. The system must allow users to rename nodes through the toolbar
4.4. The system must validate and sanitize node names

### 5. HTML Code View
5.1. The system must provide a modal view for displaying HTML code
5.2. The system must display raw HTML with proper formatting
5.3. The system must provide a copy-to-clipboard function
5.4. The modal must be responsive and accessible on all screen sizes

### 6. Mobile Support
6.1. The system must show delete button in toolbar only on mobile screens
6.2. The system must ensure all touch targets are at least 44x44px
6.3. The system must maintain proper spacing for touch interactions

## Non-Goals (Out of Scope)
1. Multi-node selection and bulk actions
2. HTML syntax highlighting
3. Tailwind class visualization
4. Minimum/maximum size constraints
5. Aspect ratio locking during resize
6. Advanced mobile gestures

## Design Considerations
1. Use subtle border/shadow for selected state
2. Toolbar should use consistent styling with existing UI
3. Modal should follow existing app's modal design patterns
4. Resize handles should be visible but not intrusive
5. Mobile delete button should be easily accessible but not prone to accidental taps

## Technical Considerations
1. Integrate with existing React Flow node selection API
2. Use React Flow's built-in resize functionality if available
3. Leverage existing LocalStorage implementation for persisting node names
4. Ensure proper cleanup of event listeners and subscriptions
5. Use responsive design breakpoints consistent with current implementation

## Success Metrics
1. Users can successfully perform all node operations (select, resize, rename, view code)
2. No reported issues with toolbar positioning or visibility
3. No reported issues with mobile delete functionality
4. Successful persistence of node names across sessions
5. Positive user feedback on resize handle usability

## Open Questions
1. Should node names be included in LocalStorage persistence?
2. Should we implement undo/redo for node operations?
3. Should we add keyboard shortcuts for common actions?
4. Should we consider adding a node search/filter feature in the future?
5. Should we implement node templates with predefined sizes? 