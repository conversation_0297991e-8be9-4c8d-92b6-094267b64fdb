## Relevant Files
- `src/components/canvas/HtmlMockupNode.tsx` - Main component that needs enhancement with new features
- `src/lib/types.ts` - Update types to support new node properties (name, dimensions, etc.)
- `src/hooks/useMockupNodes.ts` - Enhance hook to support node selection and naming
- `src/components/ui/Modal.tsx` - Create reusable modal component for HTML code view
- `src/components/canvas/NodeToolbar.tsx` - New component for the floating toolbar
- `src/components/canvas/ResizeHandle.tsx` - New component for resize functionality
- `src/hooks/useNodeSelection.ts` - New hook to manage node selection state
- `src/hooks/useNodeResize.ts` - New hook to handle node resizing logic
- `src/styles/node.css` - Styles for node selection, toolbar, and resize handles

### Notes
- Components should follow the project's existing patterns for client components (using 'use client')
- Use Tailwind CSS for styling to maintain consistency
- Ensure proper TypeScript types for all new components and hooks
- Follow React Flow's guidelines for custom node enhancements
- Use existing LocalStorage implementation for persistence

## Tasks

- [x] 1.0 Setup Node Selection System
  Note: Review React Flow documentation in reactflow_documentation.md for node selection and event handling best practices.
  - [x] 1.1 Create useNodeSelection hook with selection state management
  - [x] 1.2 Update HtmlMockupNodeData type to include selection state
  - [x] 1.3 Modify HtmlMockupNode to handle click events for selection
  - [x] 1.4 Add selected state visual feedback using Tailwind CSS
  - [x] 1.5 Update useMockupNodes to track selected node ID
  - [x] 1.6 Implement click-away listener to deselect nodes
  - [x] 1.7 Add keyboard support for node selection (Tab, Enter)

- [x] 2.0 Create Floating Toolbar Component
  Note: Review React Flow documentation in reactflow_documentation.md for node overlay and positioning guidelines.
  - [x] 2.1 Create NodeToolbar component with basic structure
  - [x] 2.2 Implement toolbar positioning logic relative to selected node
  - [x] 2.3 Add responsive styles for toolbar layout
  - [x] 2.4 Create mobile detection utility for conditional delete button
  - [x] 2.5 Style toolbar to match existing UI design
  - [x] 2.6 Add toolbar animation for smooth appearance/disappearance
  - [x] 2.7 Ensure toolbar follows node during drag operations
  - [x] 2.8 Add click-away handling to hide toolbar

- [x] 3.0 Implement Node Resizing
  Note: Review React Flow documentation in reactflow_documentation.md for node resizing and dimension handling.
  - [x] 3.1 Create ResizeHandle component for corners/edges
  - [x] 3.2 Create useNodeResize hook for resize calculations
  - [x] 3.3 Update HtmlMockupNodeData to store node dimensions
  - [x] 3.4 Add resize handles to selected nodes
  - [x] 3.5 Implement dimension display during resize
  - [x] 3.6 Add smooth transition animations for resizing
  - [x] 3.7 Ensure proper touch target sizes for mobile
  - [x] 3.8 Update LocalStorage to persist node dimensions

- [~] 4.0 Add Node Naming System (Needs UX Improvements)
  Note: Review React Flow documentation in reactflow_documentation.md for node data management and persistence.
  - [x] 4.1 Update HtmlMockupNodeData to include node name
  - [x] 4.2 Create utility for generating unique default names
  - [x] 4.3 Add name input field to NodeToolbar
  - [x] 4.4 Implement name validation and sanitization
  - [x] 4.5 Create name collision detection system
  - [x] 4.6 Update LocalStorage to persist node names
  - [x] 4.7 Add error handling for invalid names
  - [x] 4.8 Fix input interaction issues (stop event propagation)

- [~] 5.0 Create HTML Code View Modal
  Note: Review React Flow documentation in reactflow_documentation.md for node data access and custom UI integration.
  - [x] 5.1 Create reusable Modal component
  - [x] 5.2 Add HTML code formatting utility
  - [x] 5.3 Implement copy-to-clipboard functionality
  - [x] 5.4 Add success/error toast notifications
  - [x] 5.5 Style code display area for readability
  - [x] 5.6 Add keyboard shortcuts (Esc to close)
  - [x] 5.7 Ensure modal is responsive on all screen sizes
  - [ ] 5.8 Add loading state for large HTML content

I have generated the high-level tasks based on the PRD. Ready to generate the sub-tasks? Respond with 'Go' to proceed. 